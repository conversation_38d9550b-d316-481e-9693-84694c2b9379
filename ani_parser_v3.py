#!/usr/bin/env python3
"""
ANI Parser V3 - <PERSON><PERSON><PERSON> ch<PERSON>h xác dựa trên phân tích chi tiết
Phân tích đúng cấu trúc file .ani với bone names và animation data
"""

import struct
import os
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class Transform:
    position: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    rotation: Tuple[float, float, float, float] = (0.0, 0.0, 0.0, 1.0)  # quaternion

@dataclass
class AnimationFrame:
    frame_number: int
    time: float
    transform: Transform

@dataclass
class BoneData:
    name: str
    parent_index: int = -1
    bind_pose: Transform = None
    animation_frames: List[AnimationFrame] = None
    
    def __post_init__(self):
        if self.bind_pose is None:
            self.bind_pose = Transform()
        if self.animation_frames is None:
            self.animation_frames = []

class ANIParserV3:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.bones: List[BoneData] = []
        self.frame_count = 0
        self.fps = 30.0
        self.data = None
        
    def load_file(self):
        """Load file vào memory"""
        with open(self.filepath, 'rb') as f:
            self.data = f.read()
        print(f"Loaded {len(self.data)} bytes from {self.filepath}")
        
    def find_bone_positions(self) -> List[Tuple[str, int]]:
        """Tìm tất cả vị trí của bone names trong file"""
        bone_patterns = [b'Bip01', b'Bone', b'FBip01']
        bone_positions = []
        
        for pattern in bone_patterns:
            offset = 0
            while True:
                pos = self.data.find(pattern, offset)
                if pos == -1:
                    break
                    
                # Đọc tên bone đầy đủ
                end = pos
                while end < len(self.data) and self.data[end] != 0:
                    end += 1
                    
                bone_name = self.data[pos:end].decode('ascii', errors='ignore')
                
                # Kiểm tra xem có phải tên bone hợp lệ không
                if (len(bone_name) >= 3 and 
                    (bone_name.startswith('Bip01') or 
                     bone_name.startswith('Bone') or
                     bone_name.startswith('FBip01'))):
                    bone_positions.append((bone_name, pos))
                    
                offset = pos + 1
                
        # Remove duplicates và sort theo position
        unique_bones = {}
        for name, pos in bone_positions:
            if name not in unique_bones:
                unique_bones[name] = pos
                
        sorted_bones = sorted(unique_bones.items(), key=lambda x: x[1])
        return [(name, pos) for name, pos in sorted_bones]
        
    def read_transform_at_offset(self, offset: int) -> Transform:
        """Đọc transform data tại offset cụ thể"""
        if offset + 48 > len(self.data):
            return Transform()
            
        # Đọc 12 floats
        floats = []
        for i in range(12):
            val = struct.unpack('<f', self.data[offset + i*4:offset + i*4 + 4])[0]
            floats.append(val)
            
        # Parse position (3 floats đầu)
        position = (floats[0], floats[1], floats[2])
        
        # Parse rotation matrix (9 floats tiếp theo) và convert thành quaternion
        rotation_matrix = np.array([
            [floats[3], floats[4], floats[5]],
            [floats[6], floats[7], floats[8]], 
            [floats[9], floats[10], floats[11]]
        ])
        
        rotation = self.matrix_to_quaternion(rotation_matrix)
        
        return Transform(position=position, rotation=rotation)
        
    def matrix_to_quaternion(self, matrix: np.ndarray) -> Tuple[float, float, float, float]:
        """Chuyển đổi rotation matrix thành quaternion"""
        try:
            trace = matrix[0, 0] + matrix[1, 1] + matrix[2, 2]
            
            if trace > 0:
                s = np.sqrt(trace + 1.0) * 2
                w = 0.25 * s
                x = (matrix[2, 1] - matrix[1, 2]) / s
                y = (matrix[0, 2] - matrix[2, 0]) / s
                z = (matrix[1, 0] - matrix[0, 1]) / s
            else:
                # Fallback to identity
                w, x, y, z = 1.0, 0.0, 0.0, 0.0
                
            return (x, y, z, w)
        except:
            return (0.0, 0.0, 0.0, 1.0)
            
    def find_animation_data_for_bone(self, bone_name: str, bone_pos: int) -> List[AnimationFrame]:
        """Tìm animation data cho bone cụ thể"""
        frames = []
        
        # Tìm vị trí sau bone name và transform data
        name_end = bone_pos + len(bone_name) + 1
        
        # Skip padding đến transform data
        transform_start = name_end
        while transform_start < len(self.data) and self.data[transform_start] == 0:
            transform_start += 1
            
        # Skip transform data (48 bytes)
        anim_search_start = transform_start + 48
        
        # Tìm animation data trong vùng tiếp theo
        # Animation data có thể có pattern: frame_count + keyframes
        for search_offset in range(anim_search_start, min(anim_search_start + 200, len(self.data) - 16), 4):
            try:
                # Thử đọc frame count
                potential_frame_count = struct.unpack('<I', self.data[search_offset:search_offset+4])[0]
                
                # Kiểm tra xem có hợp lý không
                if 1 <= potential_frame_count <= 100:
                    # Thử đọc keyframes
                    keyframe_offset = search_offset + 4
                    valid_frames = 0
                    
                    for frame_idx in range(min(potential_frame_count, 10)):
                        if keyframe_offset + 16 > len(self.data):
                            break
                            
                        # Đọc time và position
                        time = struct.unpack('<f', self.data[keyframe_offset:keyframe_offset+4])[0]
                        pos_x = struct.unpack('<f', self.data[keyframe_offset+4:keyframe_offset+8])[0]
                        pos_y = struct.unpack('<f', self.data[keyframe_offset+8:keyframe_offset+12])[0]
                        pos_z = struct.unpack('<f', self.data[keyframe_offset+12:keyframe_offset+16])[0]
                        
                        # Kiểm tra xem có hợp lý không
                        if (0 <= time <= 10.0 and 
                            abs(pos_x) < 100 and abs(pos_y) < 100 and abs(pos_z) < 100):
                            
                            transform = Transform(position=(pos_x, pos_y, pos_z))
                            frame = AnimationFrame(frame_number=frame_idx, time=time, transform=transform)
                            frames.append(frame)
                            valid_frames += 1
                            keyframe_offset += 16
                        else:
                            break
                            
                    # Nếu tìm thấy frames hợp lệ, return
                    if valid_frames > 0:
                        print(f"  Found {valid_frames} animation frames for {bone_name}")
                        return frames
                        
            except:
                continue
                
        # Nếu không tìm thấy animation data, tạo frame mặc định
        default_transform = Transform()
        frames.append(AnimationFrame(frame_number=0, time=0.0, transform=default_transform))
        return frames
        
    def parse_file(self):
        """Parse toàn bộ file"""
        if not self.data:
            self.load_file()
            
        # Tìm tất cả bone positions
        bone_positions = self.find_bone_positions()
        print(f"Found {len(bone_positions)} bones:")
        
        for bone_name, bone_pos in bone_positions:
            print(f"  {bone_name} at 0x{bone_pos:08x}")
            
            # Tìm transform data cho bone này
            name_end = bone_pos + len(bone_name) + 1
            
            # Skip padding đến transform data
            transform_offset = name_end
            while transform_offset < len(self.data) and self.data[transform_offset] == 0:
                transform_offset += 1
                
            # Đọc bind pose
            bind_pose = self.read_transform_at_offset(transform_offset)
            
            # Tìm animation data
            animation_frames = self.find_animation_data_for_bone(bone_name, bone_pos)
            
            # Tạo bone object
            bone = BoneData(name=bone_name, bind_pose=bind_pose, animation_frames=animation_frames)
            self.bones.append(bone)
            
        print(f"\nParsed {len(self.bones)} bones total")
        
        # Tính frame count
        if self.bones:
            max_frames = max(len(bone.animation_frames) for bone in self.bones)
            self.frame_count = max_frames
            print(f"Animation has {self.frame_count} frames")
            
    def get_bone_transform_at_frame(self, bone_name: str, frame: int) -> Optional[Transform]:
        """Lấy transform của bone tại frame cụ thể"""
        for bone in self.bones:
            if bone.name == bone_name:
                if frame < len(bone.animation_frames):
                    return bone.animation_frames[frame].transform
                elif bone.animation_frames:
                    return bone.animation_frames[-1].transform
                else:
                    return bone.bind_pose
        return None
        
    def get_bone_hierarchy(self) -> Dict[str, str]:
        """Xây dựng bone hierarchy dựa trên naming convention"""
        hierarchy = {}
        bone_names = [bone.name for bone in self.bones]
        
        # Định nghĩa parent-child relationships
        parent_rules = [
            ("Bip01 Spine", "Bip01 Pelvis"),
            ("Bip01 Spine1", "Bip01 Spine"),
            ("Bip01 Neck", "Bip01 Spine1"),
            ("Bip01 Head", "Bip01 Neck"),
            ("Bip01 L Clavicle", "Bip01 Spine1"),
            ("Bip01 L UpperArm", "Bip01 L Clavicle"),
            ("Bip01 L Forearm", "Bip01 L UpperArm"),
            ("Bip01 L Hand", "Bip01 L Forearm"),
            ("Bip01 L Finger0", "Bip01 L Hand"),
            ("Bip01 L Finger01", "Bip01 L Finger0"),
            ("Bip01 R Clavicle", "Bip01 Spine1"),
            ("Bip01 R UpperArm", "Bip01 R Clavicle"),
            ("Bip01 R Forearm", "Bip01 R UpperArm"),
            ("Bip01 R Hand", "Bip01 R Forearm"),
            ("Bip01 R Finger0", "Bip01 R Hand"),
            ("Bip01 R Finger01", "Bip01 R Finger0"),
            ("Bip01 L Thigh", "Bip01 Pelvis"),
            ("Bip01 L Calf", "Bip01 L Thigh"),
            ("Bip01 L Foot", "Bip01 L Calf"),
            ("Bip01 L Toe0", "Bip01 L Foot"),
            ("Bip01 R Thigh", "Bip01 Pelvis"),
            ("Bip01 R Calf", "Bip01 R Thigh"),
            ("Bip01 R Foot", "Bip01 R Calf"),
            ("Bip01 R Toe0", "Bip01 R Foot"),
        ]
        
        for child_name, parent_name in parent_rules:
            if child_name in bone_names and parent_name in bone_names:
                hierarchy[child_name] = parent_name
                
        return hierarchy
        
    def print_summary(self):
        """In tóm tắt kết quả parse"""
        print(f"\n=== ANI FILE SUMMARY ===")
        print(f"File: {self.filepath}")
        print(f"Bones: {len(self.bones)}")
        print(f"Animation frames: {self.frame_count}")
        print(f"FPS: {self.fps}")
        
        print(f"\nBone list:")
        for i, bone in enumerate(self.bones):
            pos = bone.bind_pose.position
            frames = len(bone.animation_frames)
            print(f"  {i+1:2d}. {bone.name:<20} pos:[{pos[0]:6.3f}, {pos[1]:6.3f}, {pos[2]:6.3f}] frames:{frames}")
            
        # Print hierarchy
        hierarchy = self.get_bone_hierarchy()
        print(f"\nBone hierarchy ({len(hierarchy)} connections):")
        for child, parent in hierarchy.items():
            print(f"  {child} -> {parent}")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python ani_parser_v3.py <ani_file>")
        return
        
    filepath = sys.argv[1]
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return
        
    parser = ANIParserV3(filepath)
    parser.parse_file()
    parser.print_summary()

if __name__ == "__main__":
    main()
