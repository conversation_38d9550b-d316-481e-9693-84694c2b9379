#!/usr/bin/env python3
"""
Event Analyzer - Tìm damage trigger flags trong animation files
Phân tích để tìm thời điểm gây damage trong attack animations
"""

import struct
import os
import json
from typing import List, Tuple, Dict
from dataclasses import dataclass

@dataclass
class AnimationEvent:
    offset: int
    event_type: int
    time: float
    parameters: List[float] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = []

class EventAnalyzer:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.data = None
        self.events: List[AnimationEvent] = []
        self.animation_length = 0.0
        
    def load_file(self):
        """Load file vào memory"""
        with open(self.filepath, 'rb') as f:
            self.data = f.read()
        print(f"Loaded {len(self.data)} bytes from {self.filepath}")
        
    def find_potential_events(self) -> List[AnimationEvent]:
        """Tìm các pattern có thể là animation events"""
        events = []
        
        # Scan từ sau header (0x200) đến cuối file
        for offset in range(0x200, len(self.data) - 16, 4):
            try:
                # Pattern 1: int (event_type) + float (time) + optional params
                event_type = struct.unpack('<I', self.data[offset:offset+4])[0]
                
                # Event types thường là số nhỏ (1-20)
                if 1 <= event_type <= 20:
                    # Check next value as time
                    if offset + 8 <= len(self.data):
                        time_val = struct.unpack('<f', self.data[offset+4:offset+8])[0]
                        
                        # Time values hợp lý cho animation (0-10 seconds)
                        if 0 <= time_val <= 10.0:
                            # Read additional parameters
                            params = []
                            param_offset = offset + 8
                            
                            # Try to read up to 4 more floats as parameters
                            for i in range(4):
                                if param_offset + 4 <= len(self.data):
                                    try:
                                        param = struct.unpack('<f', self.data[param_offset:param_offset+4])[0]
                                        # Parameters should be reasonable values
                                        if abs(param) < 1000000:
                                            params.append(param)
                                            param_offset += 4
                                        else:
                                            break
                                    except:
                                        break
                                else:
                                    break
                                    
                            event = AnimationEvent(
                                offset=offset,
                                event_type=event_type,
                                time=time_val,
                                parameters=params
                            )
                            events.append(event)
                            
            except:
                continue
                
        return events
        
    def find_damage_triggers(self) -> List[AnimationEvent]:
        """Tìm damage trigger events cụ thể"""
        all_events = self.find_potential_events()
        
        # Filter events có thể là damage triggers
        damage_events = []
        
        for event in all_events:
            # Damage triggers thường có:
            # 1. Event type trong khoảng 1-5 (attack events)
            # 2. Time trong khoảng 0.1-3.0s (reasonable attack timing)
            # 3. Có parameters (damage value, effect type, etc.)
            
            if (1 <= event.event_type <= 5 and 
                0.1 <= event.time <= 3.0 and
                len(event.parameters) >= 1):
                damage_events.append(event)
                
        return damage_events
        
    def analyze_event_patterns(self):
        """Phân tích patterns của events"""
        events = self.find_potential_events()
        
        print(f"\nFound {len(events)} potential events:")
        
        # Group by event type
        event_types = {}
        for event in events:
            if event.event_type not in event_types:
                event_types[event.event_type] = []
            event_types[event.event_type].append(event)
            
        for event_type, type_events in sorted(event_types.items()):
            print(f"\nEvent Type {event_type}: {len(type_events)} occurrences")
            
            for i, event in enumerate(type_events[:5]):  # Show first 5
                params_str = ", ".join(f"{p:.3f}" for p in event.parameters[:3])
                print(f"  {i+1}. Time: {event.time:.3f}s, Params: [{params_str}], Offset: 0x{event.offset:08x}")
                
            if len(type_events) > 5:
                print(f"  ... and {len(type_events) - 5} more")
                
    def find_attack_timing(self):
        """Tìm timing của attack damage"""
        damage_events = self.find_damage_triggers()
        
        print(f"\n=== DAMAGE TRIGGER ANALYSIS ===")
        print(f"Found {len(damage_events)} potential damage triggers:")
        
        for i, event in enumerate(damage_events):
            print(f"\nDamage Trigger {i+1}:")
            print(f"  Time: {event.time:.3f} seconds from animation start")
            print(f"  Event Type: {event.event_type}")
            print(f"  Parameters: {event.parameters}")
            print(f"  File Offset: 0x{event.offset:08x}")
            
            # Show hex context
            start = max(0, event.offset - 16)
            end = min(len(self.data), event.offset + 32)
            print(f"  Hex Context:")
            for j in range(start, end, 16):
                hex_part = ' '.join(f'{b:02x}' for b in self.data[j:j+16])
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in self.data[j:j+16])
                marker = '***' if j <= event.offset < j + 16 else '   '
                print(f"    {j:08x}: {hex_part:<48} {ascii_part} {marker}")
                
        return damage_events
        
    def analyze_multiple_files(self, file_list: List[str]):
        """Phân tích nhiều file để tìm pattern chung"""
        all_damage_events = {}
        
        for filepath in file_list:
            if os.path.exists(filepath):
                print(f"\n--- Analyzing {filepath} ---")
                self.filepath = filepath
                self.load_file()
                
                damage_events = self.find_damage_triggers()
                all_damage_events[filepath] = damage_events
                
                if damage_events:
                    print(f"Damage triggers in {filepath}:")
                    for event in damage_events:
                        print(f"  Time: {event.time:.3f}s, Type: {event.event_type}")
                else:
                    print(f"No clear damage triggers found in {filepath}")
                    
        return all_damage_events
        
    def export_events_to_json(self, output_file: str):
        """Export events ra JSON file"""
        events = self.find_potential_events()
        damage_events = self.find_damage_triggers()
        
        export_data = {
            'file': self.filepath,
            'total_events': len(events),
            'damage_triggers': len(damage_events),
            'events': [],
            'damage_triggers_detail': []
        }
        
        # Export all events
        for event in events:
            export_data['events'].append({
                'offset': f"0x{event.offset:08x}",
                'type': event.event_type,
                'time': event.time,
                'parameters': event.parameters
            })
            
        # Export damage triggers
        for event in damage_events:
            export_data['damage_triggers_detail'].append({
                'offset': f"0x{event.offset:08x}",
                'type': event.event_type,
                'time': event.time,
                'parameters': event.parameters,
                'description': f"Damage trigger at {event.time:.3f}s"
            })
            
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2)
            
        print(f"\nEvents exported to {output_file}")

def main():
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python event_analyzer.py <ani_file> [ani_file2] ...")
        print("Example: python event_analyzer.py AMCA0150.ani")
        return
        
    analyzer = EventAnalyzer(sys.argv[1])
    analyzer.load_file()
    
    if len(sys.argv) == 2:
        # Single file analysis
        print(f"=== EVENT ANALYSIS FOR {sys.argv[1]} ===")
        
        # General event analysis
        analyzer.analyze_event_patterns()
        
        # Damage trigger analysis
        damage_events = analyzer.find_attack_timing()
        
        # Export results
        base_name = os.path.splitext(sys.argv[1])[0]
        analyzer.export_events_to_json(f"{base_name}_events.json")
        
        if damage_events:
            print(f"\n=== SUMMARY ===")
            print(f"Attack animation: {sys.argv[1]}")
            print(f"Damage triggers found: {len(damage_events)}")
            for i, event in enumerate(damage_events):
                print(f"  Trigger {i+1}: {event.time:.3f}s (Type {event.event_type})")
        else:
            print(f"\n=== SUMMARY ===")
            print(f"No clear damage triggers found in {sys.argv[1]}")
            print("This might be a non-attack animation or events are in different format")
            
    else:
        # Multiple file analysis
        file_list = sys.argv[1:]
        print(f"=== BATCH EVENT ANALYSIS FOR {len(file_list)} FILES ===")
        
        all_events = analyzer.analyze_multiple_files(file_list)
        
        # Summary
        print(f"\n=== BATCH SUMMARY ===")
        total_files_with_damage = 0
        for filepath, events in all_events.items():
            if events:
                total_files_with_damage += 1
                print(f"{os.path.basename(filepath)}: {len(events)} damage triggers")
                
        print(f"\nFiles with damage triggers: {total_files_with_damage}/{len(file_list)}")

if __name__ == "__main__":
    main()
