#!/usr/bin/env python3
"""
Exporter để xuất skeleton và animation từ file .ani sang các format khác
Hỗ trợ: JSON, OBJ (skeleton), CSV (animation data)
"""

import json
import os
import math
from ani_analyzer import ANIFile
from typing import Dict, List, Any

class ANIExporter:
    def __init__(self, ani_file: ANIFile):
        self.ani_file = ani_file
        
    def export_to_json(self, output_file: str):
        """Export skeleton và animation data sang JSON"""
        data = {
            'metadata': {
                'source_file': self.ani_file.filepath,
                'bone_count': len(self.ani_file.bones),
                'animation_tracks': len(self.ani_file.animation_tracks),
                'fps': self.ani_file.fps
            },
            'bones': [],
            'animation': []
        }
        
        # Export bones
        for bone in self.ani_file.bones:
            bone_data = {
                'name': bone.name,
                'position': {
                    'x': bone.position[0],
                    'y': bone.position[1], 
                    'z': bone.position[2]
                },
                'rotation': {
                    'x': bone.rotation[0],
                    'y': bone.rotation[1],
                    'z': bone.rotation[2],
                    'w': bone.rotation[3]
                },
                'scale': {
                    'x': bone.scale[0],
                    'y': bone.scale[1],
                    'z': bone.scale[2]
                }
            }
            data['bones'].append(bone_data)
            
        # Export animation tracks
        for track in self.ani_file.animation_tracks:
            track_data = {
                'bone_name': track.bone_name,
                'keyframes': []
            }
            
            for keyframe in track.keyframes:
                kf_data = {
                    'time': keyframe.time
                }
                
                if keyframe.position:
                    kf_data['position'] = {
                        'x': keyframe.position[0],
                        'y': keyframe.position[1],
                        'z': keyframe.position[2]
                    }
                    
                if keyframe.rotation:
                    kf_data['rotation'] = {
                        'x': keyframe.rotation[0],
                        'y': keyframe.rotation[1],
                        'z': keyframe.rotation[2],
                        'w': keyframe.rotation[3]
                    }
                    
                if keyframe.scale:
                    kf_data['scale'] = {
                        'x': keyframe.scale[0],
                        'y': keyframe.scale[1],
                        'z': keyframe.scale[2]
                    }
                    
                track_data['keyframes'].append(kf_data)
                
            data['animation'].append(track_data)
            
        # Save to file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        print(f"Exported to JSON: {output_file}")
        
    def export_skeleton_to_obj(self, output_file: str):
        """Export skeleton structure sang OBJ format (chỉ bones, không có mesh)"""
        lines = []
        lines.append("# Skeleton exported from ANI file")
        lines.append(f"# Source: {self.ani_file.filepath}")
        lines.append(f"# Bones: {len(self.ani_file.bones)}")
        lines.append("")
        
        # Vertices (bone positions)
        vertex_index = 1
        bone_indices = {}
        
        for bone in self.ani_file.bones:
            lines.append(f"v {bone.position[0]:.6f} {bone.position[1]:.6f} {bone.position[2]:.6f}")
            bone_indices[bone.name] = vertex_index
            vertex_index += 1
            
        lines.append("")
        
        # Build hierarchy để tạo lines
        hierarchy = self.build_bone_hierarchy()
        
        # Lines (bone connections)
        for bone in self.ani_file.bones:
            if bone.name in hierarchy:
                parent_name = hierarchy[bone.name]
                if parent_name in bone_indices:
                    child_idx = bone_indices[bone.name]
                    parent_idx = bone_indices[parent_name]
                    lines.append(f"l {parent_idx} {child_idx}")
                    
        # Save to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
            
        print(f"Exported skeleton to OBJ: {output_file}")
        
    def build_bone_hierarchy(self):
        """Build bone hierarchy (copy từ viewer)"""
        bone_map = {bone.name: bone for bone in self.ani_file.bones}
        hierarchy = {}
        
        # Parent-child relationships
        parent_rules = [
            ("Bip01 Spine", "Bip01 Pelvis"),
            ("Bip01 Spine1", "Bip01 Spine"),
            ("Bip01 Neck", "Bip01 Spine1"),
            ("Bip01 Head", "Bip01 Neck"),
            ("Bip01 L Clavicle", "Bip01 Spine1"),
            ("Bip01 L UpperArm", "Bip01 L Clavicle"),
            ("Bip01 L Forearm", "Bip01 L UpperArm"),
            ("Bip01 L Hand", "Bip01 L Forearm"),
            ("Bip01 L Finger0", "Bip01 L Hand"),
            ("Bip01 L Finger01", "Bip01 L Finger0"),
            ("Bip01 R Clavicle", "Bip01 Spine1"),
            ("Bip01 R UpperArm", "Bip01 R Clavicle"),
            ("Bip01 R Forearm", "Bip01 R UpperArm"),
            ("Bip01 R Hand", "Bip01 R Forearm"),
            ("Bip01 R Finger0", "Bip01 R Hand"),
            ("Bip01 R Finger01", "Bip01 R Finger0"),
            ("Bip01 L Thigh", "Bip01 Pelvis"),
            ("Bip01 L Calf", "Bip01 L Thigh"),
            ("Bip01 L Foot", "Bip01 L Calf"),
            ("Bip01 L Toe0", "Bip01 L Foot"),
            ("Bip01 R Thigh", "Bip01 Pelvis"),
            ("Bip01 R Calf", "Bip01 R Thigh"),
            ("Bip01 R Foot", "Bip01 R Calf"),
            ("Bip01 R Toe0", "Bip01 R Foot"),
        ]
        
        for child_name, parent_name in parent_rules:
            if child_name in bone_map and parent_name in bone_map:
                hierarchy[child_name] = parent_name
                
        return hierarchy
        
    def export_animation_to_csv(self, output_file: str):
        """Export animation data sang CSV format"""
        lines = []
        lines.append("bone_name,time,pos_x,pos_y,pos_z,rot_x,rot_y,rot_z,rot_w")
        
        for track in self.ani_file.animation_tracks:
            for keyframe in track.keyframes:
                pos = keyframe.position if keyframe.position else (0, 0, 0)
                rot = keyframe.rotation if keyframe.rotation else (0, 0, 0, 1)
                
                line = f"{track.bone_name},{keyframe.time:.6f},"
                line += f"{pos[0]:.6f},{pos[1]:.6f},{pos[2]:.6f},"
                line += f"{rot[0]:.6f},{rot[1]:.6f},{rot[2]:.6f},{rot[3]:.6f}"
                
                lines.append(line)
                
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
            
        print(f"Exported animation to CSV: {output_file}")
        
    def export_bone_hierarchy_text(self, output_file: str):
        """Export bone hierarchy sang text format dễ đọc"""
        lines = []
        lines.append(f"Bone Hierarchy - {self.ani_file.filepath}")
        lines.append("=" * 50)
        lines.append("")
        
        hierarchy = self.build_bone_hierarchy()
        bone_map = {bone.name: bone for bone in self.ani_file.bones}
        
        # Tìm root bones (không có parent)
        root_bones = []
        for bone in self.ani_file.bones:
            if bone.name not in hierarchy:
                root_bones.append(bone)
                
        def print_bone_tree(bone, level=0):
            indent = "  " * level
            pos = bone.position
            lines.append(f"{indent}- {bone.name} [{pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}]")
            
            # Tìm children
            children = []
            for child_name, parent_name in hierarchy.items():
                if parent_name == bone.name and child_name in bone_map:
                    children.append(bone_map[child_name])
                    
            for child in children:
                print_bone_tree(child, level + 1)
                
        # Print tree cho mỗi root bone
        for root in root_bones:
            print_bone_tree(root)
            lines.append("")
            
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
            
        print(f"Exported hierarchy to text: {output_file}")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python ani_exporter.py <ani_file>")
        print("Example: python ani_exporter.py AMCA0150.ani")
        return
        
    ani_file_path = sys.argv[1]
    
    if not os.path.exists(ani_file_path):
        print(f"File not found: {ani_file_path}")
        return
        
    print(f"Loading and analyzing {ani_file_path}...")
    
    # Load ANI file
    ani_file = ANIFile(ani_file_path)
    ani_file.analyze_structure()
    
    if not ani_file.bones:
        print("No bones found in file!")
        return
        
    # Create exporter
    exporter = ANIExporter(ani_file)
    
    # Generate output filenames
    base_name = os.path.splitext(ani_file_path)[0]
    
    print(f"\nExporting {len(ani_file.bones)} bones and {len(ani_file.animation_tracks)} animation tracks...")
    
    # Export to different formats
    exporter.export_to_json(f"{base_name}.json")
    exporter.export_skeleton_to_obj(f"{base_name}_skeleton.obj")
    exporter.export_animation_to_csv(f"{base_name}_animation.csv")
    exporter.export_bone_hierarchy_text(f"{base_name}_hierarchy.txt")
    
    print(f"\nExport completed! Generated files:")
    print(f"  - {base_name}.json (complete data)")
    print(f"  - {base_name}_skeleton.obj (skeleton structure)")
    print(f"  - {base_name}_animation.csv (animation data)")
    print(f"  - {base_name}_hierarchy.txt (bone hierarchy)")

if __name__ == "__main__":
    main()
