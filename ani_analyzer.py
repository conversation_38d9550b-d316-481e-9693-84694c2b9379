#!/usr/bin/env python3
"""
Analyzer cho file .ani - Animation files từ game 3D DirectX9
Phân tích cấu trúc và extract thông tin skeleton/animation
"""

import struct
import os
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import numpy as np

@dataclass
class Bone:
    name: str
    parent_index: int = -1
    position: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    rotation: Tuple[float, float, float, float] = (0.0, 0.0, 0.0, 1.0)  # quaternion
    scale: Tuple[float, float, float] = (1.0, 1.0, 1.0)

@dataclass 
class KeyFrame:
    time: float
    position: Optional[Tuple[float, float, float]] = None
    rotation: Optional[Tuple[float, float, float, float]] = None
    scale: Optional[Tuple[float, float, float]] = None

@dataclass
class AnimationTrack:
    bone_name: str
    keyframes: List[KeyFrame]

class ANIFile:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.bones: List[Bone] = []
        self.animation_tracks: List[AnimationTrack] = []
        self.frame_count = 0
        self.fps = 30.0
        
    def read_cstring(self, data: bytes, offset: int, max_len: int = 64) -> Tuple[str, int]:
        """Đọc null-terminated string"""
        end = offset
        while end < len(data) and end < offset + max_len and data[end] != 0:
            end += 1
        return data[offset:end].decode('ascii', errors='ignore'), end + 1
    
    def find_bone_names(self, data: bytes) -> List[Tuple[str, int]]:
        """Tìm tất cả tên bone trong file"""
        bone_names = []
        
        # Tìm pattern "Bip01" và các biến thể
        patterns = [b'Bip01', b'FBip01', b'bone', b'Bone']
        
        for pattern in patterns:
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                    
                # Đọc tên bone đầy đủ
                name, _ = self.read_cstring(data, pos)
                if len(name) >= 3 and name not in [b[0] for b in bone_names]:
                    bone_names.append((name, pos))
                    
                offset = pos + 1
                
        return bone_names
    
    def parse_float_data(self, data: bytes, offset: int, count: int = 1) -> List[float]:
        """Parse float data từ binary"""
        floats = []
        for i in range(count):
            if offset + 4 <= len(data):
                value = struct.unpack('<f', data[offset:offset+4])[0]
                floats.append(value)
                offset += 4
        return floats
    
    def matrix_to_quaternion(self, matrix):
        """Convert 3x3 rotation matrix to quaternion"""
        # Simplified conversion - trong thực tế cần implement đầy đủ
        trace = matrix[0][0] + matrix[1][1] + matrix[2][2]

        if trace > 0:
            s = np.sqrt(trace + 1.0) * 2
            w = 0.25 * s
            x = (matrix[2][1] - matrix[1][2]) / s
            y = (matrix[0][2] - matrix[2][0]) / s
            z = (matrix[1][0] - matrix[0][1]) / s
        else:
            # Fallback to identity quaternion
            w, x, y, z = 1.0, 0.0, 0.0, 0.0

        return (x, y, z, w)

    def parse_bone_data(self, data: bytes, offset: int) -> Optional[Bone]:
        """Parse dữ liệu bone từ offset"""
        try:
            # Đọc tên bone
            name, name_end = self.read_cstring(data, offset)

            # Skip padding đến transform data
            transform_offset = name_end
            while transform_offset < len(data) and data[transform_offset] == 0:
                transform_offset += 1

            # Tìm pattern của transform data
            # Dựa trên phân tích, có vẻ như có nhiều float values
            # Thử tìm vị trí có float data hợp lệ
            found_transform = False
            for skip in range(0, 64, 4):  # Thử các offset khác nhau
                test_offset = transform_offset + skip
                if test_offset + 48 > len(data):
                    continue

                # Test xem có phải float data không
                test_floats = self.parse_float_data(data, test_offset, 12)
                if len(test_floats) >= 12:
                    # Kiểm tra xem có phải transform matrix hợp lệ không
                    # Transform matrix thường có giá trị trong khoảng hợp lý
                    valid = True
                    for f in test_floats[:3]:  # Position values
                        if abs(f) > 100.0:  # Quá lớn cho position
                            valid = False
                            break

                    if valid:
                        transform_offset = test_offset
                        found_transform = True
                        break

            if not found_transform:
                return None

            # Parse transform data
            floats = self.parse_float_data(data, transform_offset, 12)
            if len(floats) < 12:
                return None

            # Tạo bone object
            bone = Bone(name=name)

            # Parse position (first 3 floats)
            bone.position = (floats[0], floats[1], floats[2])

            # Parse rotation matrix (next 9 floats arranged as 3x3)
            rotation_matrix = [
                [floats[3], floats[4], floats[5]],
                [floats[6], floats[7], floats[8]],
                [floats[9], floats[10], floats[11]]
            ]

            # Convert to quaternion
            bone.rotation = self.matrix_to_quaternion(rotation_matrix)

            return bone

        except Exception as e:
            print(f"Error parsing bone at offset 0x{offset:08x}: {e}")
            return None

    def find_animation_data(self, data: bytes, bone_offset: int) -> int:
        """Tìm vị trí bắt đầu của animation data"""
        # Tìm sau bone name và transform data
        search_start = bone_offset + 100

        # Tìm pattern: frame count (int) followed by keyframe data
        for offset in range(search_start, min(len(data) - 16, search_start + 200), 4):
            # Kiểm tra xem có phải frame count không
            if offset + 8 <= len(data):
                potential_frame_count = struct.unpack('<I', data[offset:offset+4])[0]

                # Frame count hợp lý (1-1000)
                if 1 <= potential_frame_count <= 1000:
                    # Kiểm tra xem sau đó có float data không
                    next_offset = offset + 4
                    if next_offset + 12 <= len(data):
                        try:
                            test_floats = self.parse_float_data(data, next_offset, 3)
                            # Kiểm tra xem có phải time/transform data không
                            if len(test_floats) == 3:
                                return offset
                        except:
                            continue

        return -1

    def parse_animation_data(self, data: bytes, bone_offset: int) -> List[KeyFrame]:
        """Parse animation keyframes cho một bone"""
        keyframes = []

        try:
            # Tìm animation data
            anim_offset = self.find_animation_data(data, bone_offset)
            if anim_offset == -1:
                # Tạo default keyframes nếu không tìm thấy
                for i in range(3):  # 3 frames mặc định
                    time = i * (1.0 / 30.0)
                    keyframe = KeyFrame(time=time)
                    keyframes.append(keyframe)
                return keyframes

            # Đọc frame count
            frame_count = struct.unpack('<I', data[anim_offset:anim_offset+4])[0]
            frame_count = min(frame_count, 100)  # Giới hạn để tránh lỗi

            current_offset = anim_offset + 4

            # Parse từng keyframe
            for frame_idx in range(frame_count):
                if current_offset + 16 > len(data):
                    break

                # Parse time và transform data
                frame_data = self.parse_float_data(data, current_offset, 4)
                if len(frame_data) >= 4:
                    time = frame_data[0]

                    # Tạo keyframe
                    keyframe = KeyFrame(time=time)

                    # Parse position nếu có
                    if len(frame_data) >= 4:
                        keyframe.position = (frame_data[1], frame_data[2], frame_data[3])

                    keyframes.append(keyframe)
                    current_offset += 16  # Skip to next keyframe
                else:
                    break

        except Exception as e:
            print(f"Error parsing animation data: {e}")
            # Fallback to default keyframes
            for i in range(3):
                time = i * (1.0 / 30.0)
                keyframe = KeyFrame(time=time)
                keyframes.append(keyframe)

        return keyframes

    def analyze_structure(self):
        """Phân tích cấu trúc file .ani và extract bones"""
        with open(self.filepath, 'rb') as f:
            data = f.read()

        print(f"Analyzing {self.filepath}")
        print(f"File size: {len(data)} bytes")

        # Tìm bone names
        bone_names = self.find_bone_names(data)
        print(f"\nFound {len(bone_names)} potential bones:")

        # Parse bones
        for name, offset in bone_names:
            bone = self.parse_bone_data(data, offset)
            if bone:
                self.bones.append(bone)
                print(f"  ✓ {name}")

                # Parse animation data cho bone này
                keyframes = self.parse_animation_data(data, offset)
                if keyframes:
                    track = AnimationTrack(bone_name=name, keyframes=keyframes)
                    self.animation_tracks.append(track)
            else:
                print(f"  ✗ {name} (failed to parse)")

        print(f"\nSuccessfully parsed {len(self.bones)} bones")
        print(f"Found {len(self.animation_tracks)} animation tracks")

def main():
    # Phân tích tất cả file .ani trong thư mục
    ani_files = [f for f in os.listdir('.') if f.endswith('.ani')]
    
    if not ani_files:
        print("Không tìm thấy file .ani nào!")
        return
        
    print(f"Found {len(ani_files)} .ani files:")
    for f in ani_files:
        print(f"  {f}")
    
    # Phân tích file đầu tiên
    analyzer = ANIFile(ani_files[0])
    analyzer.analyze_structure()

if __name__ == "__main__":
    main()
