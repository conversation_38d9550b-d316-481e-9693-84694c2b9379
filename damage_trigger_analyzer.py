#!/usr/bin/env python3
"""
Damage Trigger Analyzer - <PERSON><PERSON> tích chi tiết damage triggers trong attack animations
Tìm thời điểm chính xác gây damage trong các animation tấn công
"""

import struct
import os
from typing import List, Tuple, Dict
from dataclasses import dataclass

@dataclass
class DamageTrigger:
    offset: int
    event_type: int
    time: float
    frame_equivalent: int
    description: str
    confidence: float  # 0-1, độ tin cậy

class DamageTriggerAnalyzer:
    def __init__(self, filepath: str, fps: float = 30.0):
        self.filepath = filepath
        self.fps = fps
        self.data = None
        self.triggers: List[DamageTrigger] = []
        
    def load_file(self):
        """Load file vào memory"""
        with open(self.filepath, 'rb') as f:
            self.data = f.read()
        print(f"Analyzing {self.filepath} ({len(self.data)} bytes)")
        
    def find_damage_triggers(self) -> List[DamageTrigger]:
        """Tìm damage triggers với độ tin cậy cao"""
        triggers = []
        
        # Pattern analysis dựa trên kết quả trước
        # Tìm các timing events trong khoảng hợp lý cho attack
        
        for offset in range(0x200, len(self.data) - 8):
            try:
                # Pattern 1: byte + float (common for events)
                if offset + 5 <= len(self.data):
                    event_type = self.data[offset]
                    time_val = struct.unpack('<f', self.data[offset+1:offset+5])[0]
                    
                    # Attack damage triggers thường có:
                    # - Event type 1-10 (attack events)
                    # - Time 0.2-2.0s (reasonable attack timing)
                    if 1 <= event_type <= 10 and 0.2 <= time_val <= 2.0:
                        confidence = self.calculate_confidence(offset, event_type, time_val)
                        
                        if confidence > 0.5:  # Only high confidence triggers
                            frame = int(time_val * self.fps)
                            description = self.get_trigger_description(event_type, time_val)
                            
                            trigger = DamageTrigger(
                                offset=offset,
                                event_type=event_type,
                                time=time_val,
                                frame_equivalent=frame,
                                description=description,
                                confidence=confidence
                            )
                            triggers.append(trigger)
                            
                # Pattern 2: short + float (alternative format)
                if offset + 6 <= len(self.data):
                    event_type = struct.unpack('<H', self.data[offset:offset+2])[0]
                    time_val = struct.unpack('<f', self.data[offset+2:offset+6])[0]
                    
                    # More restrictive for short format
                    if 1 <= event_type <= 20 and 0.2 <= time_val <= 2.0:
                        confidence = self.calculate_confidence(offset, event_type, time_val)
                        
                        if confidence > 0.6:  # Higher threshold for short format
                            frame = int(time_val * self.fps)
                            description = self.get_trigger_description(event_type, time_val)
                            
                            trigger = DamageTrigger(
                                offset=offset,
                                event_type=event_type,
                                time=time_val,
                                frame_equivalent=frame,
                                description=description,
                                confidence=confidence
                            )
                            triggers.append(trigger)
                            
            except:
                continue
                
        # Remove duplicates và sort by time
        unique_triggers = self.remove_duplicates(triggers)
        unique_triggers.sort(key=lambda x: x.time)
        
        return unique_triggers
        
    def calculate_confidence(self, offset: int, event_type: int, time_val: float) -> float:
        """Tính độ tin cậy của damage trigger"""
        confidence = 0.0
        
        # Base confidence dựa trên event type
        if 1 <= event_type <= 5:
            confidence += 0.4  # Primary attack events
        elif 6 <= event_type <= 10:
            confidence += 0.3  # Secondary events
        else:
            confidence += 0.1  # Other events
            
        # Confidence dựa trên timing
        if 0.3 <= time_val <= 1.5:
            confidence += 0.4  # Optimal attack timing
        elif 0.2 <= time_val <= 2.0:
            confidence += 0.2  # Reasonable timing
        else:
            confidence += 0.1  # Edge case timing
            
        # Check context around offset
        context_score = self.analyze_context(offset)
        confidence += context_score * 0.2
        
        return min(confidence, 1.0)
        
    def analyze_context(self, offset: int) -> float:
        """Phân tích context xung quanh offset để tăng độ tin cậy"""
        score = 0.0
        
        try:
            # Check for animation data patterns around this offset
            start = max(0, offset - 16)
            end = min(len(self.data), offset + 16)
            context = self.data[start:end]
            
            # Look for float patterns (animation data)
            float_count = 0
            for i in range(0, len(context) - 4, 4):
                try:
                    val = struct.unpack('<f', context[i:i+4])[0]
                    if abs(val) < 100:  # Reasonable float values
                        float_count += 1
                except:
                    pass
                    
            if float_count >= 3:
                score += 0.5  # Likely in animation data section
                
            # Check for bone name patterns nearby
            for i in range(max(0, offset - 100), min(len(self.data), offset + 100)):
                if self.data[i:i+5] == b'Bip01':
                    score += 0.3  # Near bone data
                    break
                    
        except:
            pass
            
        return min(score, 1.0)
        
    def get_trigger_description(self, event_type: int, time_val: float) -> str:
        """Tạo mô tả cho damage trigger"""
        descriptions = {
            1: "Primary damage trigger",
            2: "Secondary damage trigger", 
            3: "Combo damage trigger",
            4: "Critical hit trigger",
            5: "Special attack trigger",
            6: "Effect trigger",
            7: "Sound trigger",
            8: "Animation event",
            9: "Particle effect",
            10: "End attack trigger"
        }
        
        base_desc = descriptions.get(event_type, f"Unknown event type {event_type}")
        
        # Add timing description
        if time_val < 0.5:
            timing_desc = "Fast attack"
        elif time_val < 1.0:
            timing_desc = "Normal attack"
        elif time_val < 1.5:
            timing_desc = "Slow attack"
        else:
            timing_desc = "Very slow attack"
            
        return f"{base_desc} ({timing_desc})"
        
    def remove_duplicates(self, triggers: List[DamageTrigger]) -> List[DamageTrigger]:
        """Remove duplicate triggers (same time, similar offset)"""
        unique = []
        
        for trigger in triggers:
            is_duplicate = False
            
            for existing in unique:
                # Consider duplicate if time difference < 0.05s and offset difference < 10
                if (abs(trigger.time - existing.time) < 0.05 and 
                    abs(trigger.offset - existing.offset) < 10):
                    # Keep the one with higher confidence
                    if trigger.confidence > existing.confidence:
                        unique.remove(existing)
                        unique.append(trigger)
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                unique.append(trigger)
                
        return unique
        
    def analyze_file(self):
        """Phân tích file và tìm damage triggers"""
        self.load_file()
        self.triggers = self.find_damage_triggers()
        
        print(f"\n=== DAMAGE TRIGGER ANALYSIS ===")
        print(f"File: {os.path.basename(self.filepath)}")
        print(f"Found {len(self.triggers)} damage triggers:")
        
        if not self.triggers:
            print("❌ No damage triggers found!")
            print("This might be a non-attack animation or uses different event format.")
            return
            
        for i, trigger in enumerate(self.triggers):
            print(f"\n🎯 Damage Trigger {i+1}:")
            print(f"   ⏰ Time: {trigger.time:.3f} seconds from animation start")
            print(f"   🎬 Frame: ~{trigger.frame_equivalent} (at {self.fps} FPS)")
            print(f"   🔢 Event Type: {trigger.event_type}")
            print(f"   📝 Description: {trigger.description}")
            print(f"   ✅ Confidence: {trigger.confidence:.1%}")
            print(f"   📍 File Offset: 0x{trigger.offset:08x}")
            
            # Show hex context
            start = max(0, trigger.offset - 8)
            end = min(len(self.data), trigger.offset + 12)
            hex_data = ' '.join(f'{b:02x}' for b in self.data[start:end])
            print(f"   🔍 Hex: {hex_data}")
            
        # Summary
        print(f"\n=== SUMMARY ===")
        print(f"🗡️ Attack Animation: {os.path.basename(self.filepath)}")
        print(f"⚔️ Total Damage Triggers: {len(self.triggers)}")
        
        if self.triggers:
            earliest = min(trigger.time for trigger in self.triggers)
            latest = max(trigger.time for trigger in self.triggers)
            print(f"⏱️ Damage Window: {earliest:.3f}s - {latest:.3f}s")
            print(f"🎯 Primary Damage Time: {self.triggers[0].time:.3f}s")
            
        return self.triggers
        
    def analyze_multiple_files(self, file_list: List[str]):
        """Phân tích nhiều file attack animations"""
        all_results = {}
        
        print(f"=== BATCH DAMAGE TRIGGER ANALYSIS ===")
        print(f"Analyzing {len(file_list)} attack animation files...\n")
        
        for filepath in file_list:
            if os.path.exists(filepath):
                print(f"--- {os.path.basename(filepath)} ---")
                
                analyzer = DamageTriggerAnalyzer(filepath, self.fps)
                triggers = analyzer.analyze_file()
                all_results[filepath] = triggers
                
                print()  # Spacing
                
        # Overall summary
        print(f"=== BATCH SUMMARY ===")
        total_files = len(file_list)
        files_with_triggers = sum(1 for triggers in all_results.values() if triggers)
        
        print(f"📁 Total files analyzed: {total_files}")
        print(f"⚔️ Files with damage triggers: {files_with_triggers}")
        print(f"📊 Success rate: {files_with_triggers/total_files:.1%}")
        
        if files_with_triggers > 0:
            print(f"\n🎯 Damage trigger summary:")
            for filepath, triggers in all_results.items():
                if triggers:
                    filename = os.path.basename(filepath)
                    primary_time = triggers[0].time
                    print(f"   {filename}: {len(triggers)} triggers, primary at {primary_time:.3f}s")
                    
        return all_results

def main():
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python damage_trigger_analyzer.py <ani_file> [ani_file2] ...")
        print("Example: python damage_trigger_analyzer.py AMCA0150.ani")
        return
        
    if len(sys.argv) == 2:
        # Single file analysis
        analyzer = DamageTriggerAnalyzer(sys.argv[1])
        analyzer.analyze_file()
    else:
        # Multiple file analysis
        analyzer = DamageTriggerAnalyzer(sys.argv[1])  # Use first file for settings
        file_list = sys.argv[1:]
        analyzer.analyze_multiple_files(file_list)

if __name__ == "__main__":
    main()
