#!/usr/bin/env python3
"""
Damage Trigger Viewer - 3D Viewer với damage trigger visualization
Hiển thị animation với damage triggers được highlight
"""

import pygame
import sys
import math
import numpy as np
from pygame.locals import *
from ani_parser_v3 import ANIParserV3, Transform
from damage_trigger_analyzer import DamageTrigger<PERSON>nalyzer

try:
    from OpenGL.GL import *
    from OpenGL.GLU import *
except ImportError:
    print("Cần cài đặt PyOpenGL: pip install PyOpenGL PyOpenGL_accelerate")
    sys.exit(1)

class DamageTriggerViewer:
    def __init__(self, width=1024, height=768):
        self.width = width
        self.height = height
        self.parser = None
        self.damage_analyzer = None
        self.damage_triggers = []
        
        # Camera controls
        self.camera_distance = 3.0
        self.camera_rotation_x = 0.0
        self.camera_rotation_y = 0.0
        self.mouse_dragging = False
        self.last_mouse_pos = (0, 0)
        
        # Animation controls
        self.current_frame = 0
        self.animation_time = 0.0
        self.animation_speed = 1.0
        self.playing = False
        self.show_bones = True
        self.show_hierarchy = True
        self.show_damage_triggers = True
        
        # Colors
        self.bone_color = (1.0, 1.0, 0.0)  # Yellow
        self.joint_color = (1.0, 0.0, 0.0)  # Red
        self.hierarchy_color = (0.0, 1.0, 0.0)  # Green
        self.damage_trigger_color = (1.0, 0.0, 1.0)  # Magenta
        self.active_trigger_color = (1.0, 1.0, 1.0)  # White
        
    def init_pygame(self):
        """Khởi tạo pygame và OpenGL"""
        pygame.init()
        pygame.display.set_mode((self.width, self.height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("Damage Trigger Viewer - Attack Animation Analysis")
        
        # Setup OpenGL
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        
        # Lighting setup
        glLightfv(GL_LIGHT0, GL_POSITION, [1.0, 1.0, 1.0, 0.0])
        glLightfv(GL_LIGHT0, GL_DIFFUSE, [1.0, 1.0, 1.0, 1.0])
        glLightfv(GL_LIGHT0, GL_AMBIENT, [0.3, 0.3, 0.3, 1.0])
        
        # Perspective setup
        glMatrixMode(GL_PROJECTION)
        gluPerspective(45, (self.width / self.height), 0.1, 50.0)
        glMatrixMode(GL_MODELVIEW)
        
    def load_ani_file(self, filepath):
        """Load file .ani và analyze damage triggers"""
        try:
            # Load animation data
            self.parser = ANIParserV3(filepath)
            self.parser.parse_file()
            
            # Analyze damage triggers
            self.damage_analyzer = DamageTriggerAnalyzer(filepath)
            self.damage_triggers = self.damage_analyzer.analyze_file()
            
            print(f"Loaded {len(self.parser.bones)} bones with {self.parser.frame_count} frames")
            print(f"Found {len(self.damage_triggers)} damage triggers")
            
            return True
        except Exception as e:
            print(f"Error loading ANI file: {e}")
            return False
            
    def setup_camera(self):
        """Setup camera position và rotation"""
        glLoadIdentity()
        glTranslatef(0.0, 0.0, -self.camera_distance)
        glRotatef(self.camera_rotation_x, 1, 0, 0)
        glRotatef(self.camera_rotation_y, 0, 1, 0)
        
    def get_bone_world_transform(self, bone_name: str, frame: int) -> Transform:
        """Lấy world transform của bone tại frame cụ thể"""
        bone = None
        for b in self.parser.bones:
            if b.name == bone_name:
                bone = b
                break
                
        if not bone:
            return Transform()
            
        if frame < len(bone.animation_frames):
            anim_transform = bone.animation_frames[frame].transform
            pos = anim_transform.position
            if pos == (0.0, 0.0, 0.0):
                pos = bone.bind_pose.position
            return Transform(position=pos, rotation=anim_transform.rotation)
        else:
            return bone.bind_pose
            
    def draw_bone_joint(self, position, size=0.02, color=None):
        """Vẽ joint với color tùy chọn"""
        if color is None:
            color = self.joint_color
            
        glPushMatrix()
        glTranslatef(*position)
        glColor3f(*color)
        
        glDisable(GL_LIGHTING)
        quadric = gluNewQuadric()
        gluSphere(quadric, size, 8, 8)
        gluDeleteQuadric(quadric)
        glEnable(GL_LIGHTING)
        
        glPopMatrix()
        
    def draw_bone_connection(self, start_pos, end_pos, color=None):
        """Vẽ connection với color tùy chọn"""
        if color is None:
            color = self.hierarchy_color
            
        glDisable(GL_LIGHTING)
        glColor3f(*color)
        glLineWidth(2.0)
        
        glBegin(GL_LINES)
        glVertex3f(*start_pos)
        glVertex3f(*end_pos)
        glEnd()
        
        glEnable(GL_LIGHTING)
        
    def draw_damage_trigger_indicator(self, position, size=0.05):
        """Vẽ indicator cho damage trigger"""
        glPushMatrix()
        glTranslatef(*position)
        
        # Vẽ sphere lớn hơn với màu đặc biệt
        glColor3f(*self.damage_trigger_color)
        glDisable(GL_LIGHTING)
        
        # Animated pulsing effect
        pulse = 1.0 + 0.3 * math.sin(self.animation_time * 10)
        
        quadric = gluNewQuadric()
        gluSphere(quadric, size * pulse, 12, 12)
        gluDeleteQuadric(quadric)
        
        # Draw wireframe overlay
        glColor3f(1.0, 1.0, 1.0)
        glPolygonMode(GL_FRONT_AND_BACK, GL_LINE)
        quadric = gluNewQuadric()
        gluSphere(quadric, size * pulse * 1.1, 8, 8)
        gluDeleteQuadric(quadric)
        glPolygonMode(GL_FRONT_AND_BACK, GL_FILL)
        
        glEnable(GL_LIGHTING)
        glPopMatrix()
        
    def get_active_damage_triggers(self) -> list:
        """Lấy damage triggers active tại thời điểm hiện tại"""
        active_triggers = []
        
        for trigger in self.damage_triggers:
            # Trigger active trong khoảng ±0.1s
            time_diff = abs(self.animation_time - trigger.time)
            if time_diff <= 0.1:
                active_triggers.append(trigger)
                
        return active_triggers
        
    def draw_skeleton_at_frame(self, frame):
        """Vẽ skeleton với damage trigger indicators"""
        if not self.parser or not self.parser.bones:
            return
            
        hierarchy = self.parser.get_bone_hierarchy()
        bone_transforms = {}
        
        # Lấy transforms cho tất cả bones
        for bone in self.parser.bones:
            transform = self.get_bone_world_transform(bone.name, frame)
            bone_transforms[bone.name] = transform
            
        # Vẽ hierarchy connections
        if self.show_hierarchy:
            for child_name, parent_name in hierarchy.items():
                if child_name in bone_transforms and parent_name in bone_transforms:
                    child_pos = bone_transforms[child_name].position
                    parent_pos = bone_transforms[parent_name].position
                    
                    if (abs(child_pos[0]) < 100 and abs(child_pos[1]) < 100 and abs(child_pos[2]) < 100 and
                        abs(parent_pos[0]) < 100 and abs(parent_pos[1]) < 100 and abs(parent_pos[2]) < 100 and
                        not any(math.isnan(x) for x in child_pos + parent_pos)):
                        self.draw_bone_connection(parent_pos, child_pos)
                        
        # Vẽ joints
        if self.show_bones:
            for bone_name, transform in bone_transforms.items():
                pos = transform.position
                if (abs(pos[0]) < 100 and abs(pos[1]) < 100 and abs(pos[2]) < 100 and
                    not (math.isnan(pos[0]) or math.isnan(pos[1]) or math.isnan(pos[2]))):
                    self.draw_bone_joint(pos)
                    
        # Vẽ damage trigger indicators
        if self.show_damage_triggers:
            active_triggers = self.get_active_damage_triggers()
            
            for trigger in self.damage_triggers:
                # Vẽ trigger indicator tại root bone position (simplified)
                root_pos = (0, 0, 0)
                if 'Bip01 Pelvis' in bone_transforms:
                    root_pos = bone_transforms['Bip01 Pelvis'].position
                elif 'Bip01' in bone_transforms:
                    root_pos = bone_transforms['Bip01'].position
                    
                if not any(math.isnan(x) for x in root_pos):
                    # Offset trigger indicators
                    trigger_pos = (
                        root_pos[0] + 0.2,
                        root_pos[1] + 0.3 + trigger.event_type * 0.1,
                        root_pos[2]
                    )
                    
                    # Highlight active triggers
                    if trigger in active_triggers:
                        self.draw_damage_trigger_indicator(trigger_pos, 0.08)
                    else:
                        self.draw_damage_trigger_indicator(trigger_pos, 0.04)
                        
    def draw_ui_overlay(self):
        """Vẽ UI overlay với thông tin damage triggers"""
        # Switch to 2D rendering
        glMatrixMode(GL_PROJECTION)
        glPushMatrix()
        glLoadIdentity()
        glOrtho(0, self.width, self.height, 0, -1, 1)
        glMatrixMode(GL_MODELVIEW)
        glPushMatrix()
        glLoadIdentity()
        
        glDisable(GL_DEPTH_TEST)
        glDisable(GL_LIGHTING)
        
        # Draw damage trigger timeline
        if self.damage_triggers:
            # Timeline background
            timeline_y = self.height - 100
            timeline_width = self.width - 40
            
            glColor3f(0.2, 0.2, 0.2)
            glBegin(GL_QUADS)
            glVertex2f(20, timeline_y - 20)
            glVertex2f(20 + timeline_width, timeline_y - 20)
            glVertex2f(20 + timeline_width, timeline_y + 20)
            glVertex2f(20, timeline_y + 20)
            glEnd()
            
            # Timeline markers
            max_time = max(trigger.time for trigger in self.damage_triggers) + 0.5
            
            for trigger in self.damage_triggers:
                x_pos = 20 + (trigger.time / max_time) * timeline_width
                
                # Active trigger
                if abs(self.animation_time - trigger.time) <= 0.1:
                    glColor3f(*self.active_trigger_color)
                    glLineWidth(4.0)
                else:
                    glColor3f(*self.damage_trigger_color)
                    glLineWidth(2.0)
                    
                glBegin(GL_LINES)
                glVertex2f(x_pos, timeline_y - 15)
                glVertex2f(x_pos, timeline_y + 15)
                glEnd()
                
            # Current time indicator
            current_x = 20 + (self.animation_time / max_time) * timeline_width
            glColor3f(1.0, 1.0, 0.0)
            glLineWidth(3.0)
            glBegin(GL_LINES)
            glVertex2f(current_x, timeline_y - 20)
            glVertex2f(current_x, timeline_y + 20)
            glEnd()
            
        # Restore 3D rendering
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_LIGHTING)
        
        glPopMatrix()
        glMatrixMode(GL_PROJECTION)
        glPopMatrix()
        glMatrixMode(GL_MODELVIEW)
        
    def draw_grid(self):
        """Vẽ grid reference"""
        glDisable(GL_LIGHTING)
        glColor3f(0.3, 0.3, 0.3)
        glLineWidth(1.0)
        
        glBegin(GL_LINES)
        for i in range(-5, 6):
            glVertex3f(i * 0.2, 0, -1)
            glVertex3f(i * 0.2, 0, 1)
            glVertex3f(-1, 0, i * 0.2)
            glVertex3f(1, 0, i * 0.2)
        glEnd()
        
        # Axes
        glLineWidth(3.0)
        glBegin(GL_LINES)
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0.5, 0, 0)
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 0.5, 0)
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 0, 0.5)
        glEnd()
        
        glEnable(GL_LIGHTING)
        
    def handle_mouse(self, event):
        """Xử lý mouse input"""
        if event.type == MOUSEBUTTONDOWN:
            if event.button == 1:
                self.mouse_dragging = True
                self.last_mouse_pos = pygame.mouse.get_pos()
            elif event.button == 4:
                self.camera_distance = max(0.5, self.camera_distance - 0.2)
            elif event.button == 5:
                self.camera_distance = min(10.0, self.camera_distance + 0.2)
        elif event.type == MOUSEBUTTONUP:
            if event.button == 1:
                self.mouse_dragging = False
        elif event.type == MOUSEMOTION and self.mouse_dragging:
            mouse_pos = pygame.mouse.get_pos()
            dx = mouse_pos[0] - self.last_mouse_pos[0]
            dy = mouse_pos[1] - self.last_mouse_pos[1]
            
            self.camera_rotation_y += dx * 0.5
            self.camera_rotation_x += dy * 0.5
            self.camera_rotation_x = max(-90, min(90, self.camera_rotation_x))
            
            self.last_mouse_pos = mouse_pos
            
    def handle_keyboard(self, event):
        """Xử lý keyboard input"""
        if event.type == KEYDOWN:
            if event.key == K_SPACE:
                self.playing = not self.playing
                print(f"Animation {'playing' if self.playing else 'paused'}")
            elif event.key == K_r:
                self.current_frame = 0
                self.animation_time = 0.0
                print("Animation reset")
            elif event.key == K_t:
                self.show_damage_triggers = not self.show_damage_triggers
                print(f"Damage triggers {'visible' if self.show_damage_triggers else 'hidden'}")
            elif event.key == K_b:
                self.show_bones = not self.show_bones
                print(f"Bones {'visible' if self.show_bones else 'hidden'}")
            elif event.key == K_h:
                self.show_hierarchy = not self.show_hierarchy
                print(f"Hierarchy {'visible' if self.show_hierarchy else 'hidden'}")
            elif event.key == K_LEFT:
                self.current_frame = max(0, self.current_frame - 1)
                self.animation_time = self.current_frame / self.parser.fps
                print(f"Frame: {self.current_frame}, Time: {self.animation_time:.3f}s")
            elif event.key == K_RIGHT:
                if self.parser:
                    self.current_frame = min(self.parser.frame_count - 1, self.current_frame + 1)
                    self.animation_time = self.current_frame / self.parser.fps
                    print(f"Frame: {self.current_frame}, Time: {self.animation_time:.3f}s")
            elif event.key == K_1:
                # Jump to first damage trigger
                if self.damage_triggers:
                    self.animation_time = self.damage_triggers[0].time
                    self.current_frame = int(self.animation_time * self.parser.fps)
                    print(f"Jumped to first damage trigger at {self.animation_time:.3f}s")
                    
    def update_animation(self, dt):
        """Update animation"""
        if self.playing and self.parser:
            self.animation_time += dt * self.animation_speed
            
            frame_time = 1.0 / self.parser.fps
            new_frame = int(self.animation_time / frame_time)
            
            if new_frame != self.current_frame:
                self.current_frame = new_frame
                
                if self.current_frame >= self.parser.frame_count:
                    self.current_frame = 0
                    self.animation_time = 0.0
                    
    def render(self):
        """Render frame"""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        
        self.setup_camera()
        self.draw_grid()
        self.draw_skeleton_at_frame(self.current_frame)
        self.draw_ui_overlay()
        
        pygame.display.flip()
        
    def print_controls(self):
        """In ra controls"""
        print("\n=== DAMAGE TRIGGER VIEWER CONTROLS ===")
        print("Mouse: Drag to rotate camera")
        print("Mouse wheel: Zoom in/out")
        print("SPACE: Play/Pause animation")
        print("R: Reset animation")
        print("T: Toggle damage triggers visibility")
        print("B: Toggle bones visibility")
        print("H: Toggle hierarchy visibility")
        print("LEFT/RIGHT: Step through frames")
        print("1: Jump to first damage trigger")
        print("ESC: Exit")
        print("=======================================\n")
        
    def run(self, ani_filepath):
        """Main loop"""
        self.init_pygame()
        
        if not self.load_ani_file(ani_filepath):
            print("Failed to load ANI file!")
            return
            
        self.print_controls()
        
        print(f"Attack animation analysis:")
        print(f"  File: {ani_filepath}")
        print(f"  Bones: {len(self.parser.bones)}")
        print(f"  Frames: {self.parser.frame_count}")
        print(f"  Damage triggers: {len(self.damage_triggers)}")
        
        if self.damage_triggers:
            print(f"  Damage window: {self.damage_triggers[0].time:.3f}s - {self.damage_triggers[-1].time:.3f}s")
            
        clock = pygame.time.Clock()
        running = True
        
        while running:
            dt = clock.tick(60) / 1000.0
            
            for event in pygame.event.get():
                if event.type == QUIT or (event.type == KEYDOWN and event.key == K_ESCAPE):
                    running = False
                else:
                    self.handle_mouse(event)
                    self.handle_keyboard(event)
                    
            self.update_animation(dt)
            self.render()
            
        pygame.quit()

def main():
    if len(sys.argv) != 2:
        print("Usage: python damage_trigger_viewer.py <ani_file>")
        print("Example: python damage_trigger_viewer.py AMCA0150.ani")
        return
        
    ani_file = sys.argv[1]
    viewer = DamageTriggerViewer()
    viewer.run(ani_file)

if __name__ == "__main__":
    main()
