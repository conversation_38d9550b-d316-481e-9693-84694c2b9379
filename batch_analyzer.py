#!/usr/bin/env python3
"""
Batch analyzer để phân tích tất cả file .ani trong thư mục
So s<PERSON>h cấu trúc và tìm patterns chung
"""

import os
import json
from ani_analyzer import ANIFile
from typing import Dict, List, Any

class BatchAnalyzer:
    def __init__(self):
        self.results = {}
        self.summary = {
            'total_files': 0,
            'successful_parses': 0,
            'failed_parses': 0,
            'common_bones': set(),
            'unique_bones': set(),
            'bone_counts': [],
            'file_sizes': []
        }
        
    def analyze_all_files(self, directory='.'):
        """Phân tích tất cả file .ani trong thư mục"""
        ani_files = [f for f in os.listdir(directory) if f.endswith('.ani')]
        
        if not ani_files:
            print("Không tìm thấy file .ani nào!")
            return
            
        print(f"Tìm thấy {len(ani_files)} file .ani")
        print("<PERSON><PERSON><PERSON> đầu phân tích batch...")
        
        self.summary['total_files'] = len(ani_files)
        
        for i, filename in enumerate(ani_files):
            print(f"\n[{i+1}/{len(ani_files)}] Analyzing {filename}...")
            
            try:
                # Phân tích file
                ani_file = ANIFile(filename)
                ani_file.analyze_structure()
                
                # Lưu kết quả
                file_info = {
                    'filename': filename,
                    'file_size': os.path.getsize(filename),
                    'bone_count': len(ani_file.bones),
                    'bone_names': [bone.name for bone in ani_file.bones],
                    'animation_tracks': len(ani_file.animation_tracks),
                    'success': True
                }
                
                self.results[filename] = file_info
                self.summary['successful_parses'] += 1
                self.summary['bone_counts'].append(len(ani_file.bones))
                self.summary['file_sizes'].append(os.path.getsize(filename))
                
                # Cập nhật bone statistics
                bone_names = set(bone.name for bone in ani_file.bones)
                if not self.summary['common_bones']:
                    self.summary['common_bones'] = bone_names.copy()
                else:
                    self.summary['common_bones'] &= bone_names
                    
                self.summary['unique_bones'] |= bone_names
                
                print(f"  ✓ {len(ani_file.bones)} bones, {len(ani_file.animation_tracks)} tracks")
                
            except Exception as e:
                print(f"  ✗ Error: {e}")
                self.results[filename] = {
                    'filename': filename,
                    'success': False,
                    'error': str(e)
                }
                self.summary['failed_parses'] += 1
                
    def generate_report(self):
        """Tạo báo cáo tổng hợp"""
        print("\n" + "="*60)
        print("BATCH ANALYSIS REPORT")
        print("="*60)
        
        # Thống kê chung
        print(f"Total files analyzed: {self.summary['total_files']}")
        print(f"Successful parses: {self.summary['successful_parses']}")
        print(f"Failed parses: {self.summary['failed_parses']}")
        print(f"Success rate: {self.summary['successful_parses']/self.summary['total_files']*100:.1f}%")
        
        if self.summary['bone_counts']:
            print(f"\nBone count statistics:")
            print(f"  Min bones: {min(self.summary['bone_counts'])}")
            print(f"  Max bones: {max(self.summary['bone_counts'])}")
            print(f"  Average bones: {sum(self.summary['bone_counts'])/len(self.summary['bone_counts']):.1f}")
            
        if self.summary['file_sizes']:
            print(f"\nFile size statistics:")
            print(f"  Min size: {min(self.summary['file_sizes'])} bytes")
            print(f"  Max size: {max(self.summary['file_sizes'])} bytes")
            print(f"  Average size: {sum(self.summary['file_sizes'])/len(self.summary['file_sizes']):.0f} bytes")
            
        # Common bones
        if self.summary['common_bones']:
            print(f"\nCommon bones (present in ALL files): {len(self.summary['common_bones'])}")
            for bone in sorted(self.summary['common_bones']):
                print(f"  - {bone}")
        else:
            print("\nNo bones are common to all files")
            
        # Unique bones
        print(f"\nTotal unique bones across all files: {len(self.summary['unique_bones'])}")
        
        # File details
        print(f"\nPer-file details:")
        for filename, info in self.results.items():
            if info['success']:
                print(f"  {filename}: {info['bone_count']} bones, {info['file_size']} bytes")
            else:
                print(f"  {filename}: FAILED - {info['error']}")
                
    def save_report(self, output_file='analysis_report.json'):
        """Lưu báo cáo ra file JSON"""
        # Convert set to list for JSON serialization
        summary_copy = self.summary.copy()
        summary_copy['common_bones'] = list(summary_copy['common_bones'])
        summary_copy['unique_bones'] = list(summary_copy['unique_bones'])
        
        report_data = {
            'summary': summary_copy,
            'results': self.results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
            
        print(f"\nReport saved to {output_file}")
        
    def find_bone_patterns(self):
        """Tìm patterns trong bone naming"""
        print(f"\nBone naming patterns:")
        
        patterns = {}
        for filename, info in self.results.items():
            if not info['success']:
                continue
                
            for bone_name in info['bone_names']:
                # Phân loại theo prefix
                if bone_name.startswith('Bip01'):
                    category = 'Bip01'
                elif bone_name.startswith('Bone'):
                    category = 'Bone'
                elif bone_name.startswith('FBip01'):
                    category = 'FBip01'
                else:
                    category = 'Other'
                    
                if category not in patterns:
                    patterns[category] = set()
                patterns[category].add(bone_name)
                
        for category, bones in patterns.items():
            print(f"\n{category} bones ({len(bones)}):")
            for bone in sorted(bones):
                print(f"  - {bone}")

def main():
    analyzer = BatchAnalyzer()
    
    print("ANI Batch Analyzer")
    print("Phân tích tất cả file .ani trong thư mục hiện tại")
    
    # Phân tích tất cả files
    analyzer.analyze_all_files()
    
    # Tạo báo cáo
    analyzer.generate_report()
    
    # Tìm patterns
    analyzer.find_bone_patterns()
    
    # Lưu báo cáo
    analyzer.save_report()
    
    print(f"\nHoàn thành! Kiểm tra file analysis_report.json để xem chi tiết.")

if __name__ == "__main__":
    main()
