#!/usr/bin/env python3
"""
ANI Parser V2 - <PERSON><PERSON> tích ch<PERSON>h x<PERSON>c hơn cấu trúc file .ani
Dựa trên phân tích chi tiết format binary
"""

import struct
import os
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class Transform:
    position: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    rotation: Tuple[float, float, float, float] = (0.0, 0.0, 0.0, 1.0)  # quaternion
    scale: Tuple[float, float, float] = (1.0, 1.0, 1.0)

@dataclass
class AnimationFrame:
    frame_number: int
    time: float
    transform: Transform

@dataclass
class BoneData:
    name: str
    parent_index: int = -1
    bind_pose: Transform = None
    animation_frames: List[AnimationFrame] = None
    
    def __post_init__(self):
        if self.bind_pose is None:
            self.bind_pose = Transform()
        if self.animation_frames is None:
            self.animation_frames = []

class ANIParserV2:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.bones: List[BoneData] = []
        self.frame_count = 0
        self.fps = 30.0
        self.data = None
        
    def load_file(self):
        """Load file vào memory"""
        with open(self.filepath, 'rb') as f:
            self.data = f.read()
        print(f"Loaded {len(self.data)} bytes from {self.filepath}")
        
    def find_data_start(self) -> int:
        """Tìm vị trí bắt đầu của dữ liệu thực (sau padding)"""
        offset = 0
        while offset < len(self.data) and self.data[offset] == 0:
            offset += 1
        return offset
        
    def read_cstring(self, offset: int, max_len: int = 64) -> Tuple[str, int]:
        """Đọc null-terminated string"""
        end = offset
        while end < len(self.data) and end < offset + max_len and self.data[end] != 0:
            end += 1
        return self.data[offset:end].decode('ascii', errors='ignore'), end + 1
        
    def read_float(self, offset: int) -> float:
        """Đọc float 32-bit little endian"""
        if offset + 4 <= len(self.data):
            return struct.unpack('<f', self.data[offset:offset+4])[0]
        return 0.0
        
    def read_int(self, offset: int) -> int:
        """Đọc int 32-bit little endian"""
        if offset + 4 <= len(self.data):
            return struct.unpack('<I', self.data[offset:offset+4])[0]
        return 0
        
    def analyze_bone_block(self, offset: int) -> Tuple[Optional[BoneData], int]:
        """Phân tích một block bone data"""
        if offset >= len(self.data):
            return None, offset
            
        # Đọc tên bone
        bone_name, name_end = self.read_cstring(offset)
        if not bone_name:
            return None, offset + 1
            
        print(f"Found bone: {bone_name} at offset 0x{offset:08x}")
        
        # Tìm transform data sau tên bone
        # Dựa trên phân tích, có vẻ như có pattern cố định
        transform_offset = name_end
        
        # Skip padding đến transform data
        while transform_offset < len(self.data) and self.data[transform_offset] == 0:
            transform_offset += 1
            
        # Kiểm tra xem có đủ data cho transform không
        if transform_offset + 48 > len(self.data):
            return None, name_end
            
        # Đọc transform data (bind pose)
        bind_pose = self.read_transform(transform_offset)
        
        # Tạo bone object
        bone = BoneData(name=bone_name, bind_pose=bind_pose)
        
        # Tìm animation data
        anim_offset = transform_offset + 48
        animation_frames = self.read_animation_data(anim_offset, bone_name)
        bone.animation_frames = animation_frames
        
        # Tính toán offset cho bone tiếp theo
        # Dựa trên pattern, mỗi bone block có kích thước khác nhau
        next_offset = self.find_next_bone_offset(anim_offset)
        
        return bone, next_offset
        
    def read_transform(self, offset: int) -> Transform:
        """Đọc transform data (position + rotation matrix)"""
        # Đọc 12 floats (có thể là position + 3x3 rotation matrix)
        floats = []
        for i in range(12):
            floats.append(self.read_float(offset + i * 4))
            
        # Parse position (3 floats đầu)
        position = (floats[0], floats[1], floats[2])
        
        # Parse rotation matrix (9 floats tiếp theo)
        # Chuyển đổi thành quaternion
        rotation_matrix = np.array([
            [floats[3], floats[4], floats[5]],
            [floats[6], floats[7], floats[8]], 
            [floats[9], floats[10], floats[11]]
        ])
        
        rotation = self.matrix_to_quaternion(rotation_matrix)
        
        return Transform(position=position, rotation=rotation)
        
    def matrix_to_quaternion(self, matrix: np.ndarray) -> Tuple[float, float, float, float]:
        """Chuyển đổi rotation matrix thành quaternion"""
        # Simplified conversion
        trace = matrix[0, 0] + matrix[1, 1] + matrix[2, 2]
        
        if trace > 0:
            s = np.sqrt(trace + 1.0) * 2
            w = 0.25 * s
            x = (matrix[2, 1] - matrix[1, 2]) / s
            y = (matrix[0, 2] - matrix[2, 0]) / s
            z = (matrix[1, 0] - matrix[0, 1]) / s
        else:
            # Fallback
            w, x, y, z = 1.0, 0.0, 0.0, 0.0
            
        return (x, y, z, w)
        
    def read_animation_data(self, offset: int, bone_name: str) -> List[AnimationFrame]:
        """Đọc animation keyframes cho bone"""
        frames = []
        
        # Tìm pattern animation data
        # Có thể có frame count + keyframe data
        current_offset = offset
        
        # Thử đọc frame count
        potential_frame_count = self.read_int(current_offset)
        
        # Kiểm tra xem có hợp lý không (1-1000 frames)
        if 1 <= potential_frame_count <= 1000:
            print(f"  Found {potential_frame_count} animation frames for {bone_name}")
            current_offset += 4
            
            # Đọc keyframes
            for frame_idx in range(min(potential_frame_count, 100)):  # Giới hạn để tránh lỗi
                if current_offset + 16 > len(self.data):
                    break
                    
                # Đọc frame data (time + transform)
                time = self.read_float(current_offset)
                pos_x = self.read_float(current_offset + 4)
                pos_y = self.read_float(current_offset + 8)
                pos_z = self.read_float(current_offset + 12)
                
                transform = Transform(position=(pos_x, pos_y, pos_z))
                frame = AnimationFrame(frame_number=frame_idx, time=time, transform=transform)
                frames.append(frame)
                
                current_offset += 16  # Move to next frame
        else:
            # Không tìm thấy animation data hợp lệ, tạo frame mặc định
            default_transform = Transform()
            frames.append(AnimationFrame(frame_number=0, time=0.0, transform=default_transform))
            
        return frames
        
    def find_next_bone_offset(self, current_offset: int) -> int:
        """Tìm offset của bone tiếp theo"""
        # Tìm pattern tên bone tiếp theo
        search_start = current_offset + 50  # Skip một khoảng
        
        # Tìm chuỗi có thể là tên bone
        for offset in range(search_start, min(len(self.data) - 64, search_start + 500)):
            # Kiểm tra xem có phải tên bone không
            if offset + 10 < len(self.data):
                potential_name, _ = self.read_cstring(offset, 32)
                
                # Kiểm tra pattern tên bone
                if (potential_name.startswith('Bip01') or 
                    potential_name.startswith('Bone') or
                    potential_name.startswith('FBip01')):
                    return offset
                    
        return current_offset + 100  # Fallback
        
    def parse_file(self):
        """Parse toàn bộ file"""
        if not self.data:
            self.load_file()
            
        # Tìm vị trí bắt đầu dữ liệu
        data_start = self.find_data_start()
        print(f"Data starts at offset: 0x{data_start:08x}")
        
        # Parse từng bone
        current_offset = data_start
        bone_count = 0
        
        while current_offset < len(self.data) - 100 and bone_count < 100:  # Giới hạn để tránh infinite loop
            bone, next_offset = self.analyze_bone_block(current_offset)
            
            if bone:
                self.bones.append(bone)
                bone_count += 1
                print(f"  Parsed bone {bone_count}: {bone.name}")
            else:
                # Không tìm thấy bone, skip forward
                current_offset += 1
                continue
                
            current_offset = next_offset
            
            # Thoát nếu offset không thay đổi (tránh infinite loop)
            if next_offset <= current_offset:
                current_offset += 50
                
        print(f"\nParsed {len(self.bones)} bones total")
        
        # Tính frame count từ animation data
        if self.bones:
            max_frames = max(len(bone.animation_frames) for bone in self.bones)
            self.frame_count = max_frames
            print(f"Animation has {self.frame_count} frames")
            
    def get_bone_transform_at_frame(self, bone_name: str, frame: int) -> Optional[Transform]:
        """Lấy transform của bone tại frame cụ thể"""
        for bone in self.bones:
            if bone.name == bone_name:
                if frame < len(bone.animation_frames):
                    return bone.animation_frames[frame].transform
                elif bone.animation_frames:
                    # Return last frame if out of range
                    return bone.animation_frames[-1].transform
                else:
                    # Return bind pose
                    return bone.bind_pose
        return None
        
    def print_summary(self):
        """In tóm tắt kết quả parse"""
        print(f"\n=== ANI FILE SUMMARY ===")
        print(f"File: {self.filepath}")
        print(f"Bones: {len(self.bones)}")
        print(f"Animation frames: {self.frame_count}")
        print(f"FPS: {self.fps}")
        
        print(f"\nBone list:")
        for i, bone in enumerate(self.bones):
            pos = bone.bind_pose.position
            frames = len(bone.animation_frames)
            print(f"  {i+1:2d}. {bone.name:<20} pos:[{pos[0]:6.3f}, {pos[1]:6.3f}, {pos[2]:6.3f}] frames:{frames}")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python ani_parser_v2.py <ani_file>")
        return
        
    filepath = sys.argv[1]
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return
        
    parser = ANIParserV2(filepath)
    parser.parse_file()
    parser.print_summary()

if __name__ == "__main__":
    main()
