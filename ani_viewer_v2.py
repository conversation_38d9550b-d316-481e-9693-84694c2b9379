#!/usr/bin/env python3
"""
ANI Viewer V2 - 3D Viewer sử dụng parser V3 ch<PERSON>h x<PERSON><PERSON> thị skeleton và animation thực từ file .ani
"""

import pygame
import sys
import math
import numpy as np
from pygame.locals import *
from ani_parser_v3 import ANIParserV3, Transform

try:
    from OpenGL.GL import *
    from OpenGL.GLU import *
except ImportError:
    print("Cần cài đặt PyOpenGL: pip install PyOpenGL PyOpenGL_accelerate")
    sys.exit(1)

class SkeletonViewerV2:
    def __init__(self, width=1024, height=768):
        self.width = width
        self.height = height
        self.parser = None
        
        # Camera controls
        self.camera_distance = 3.0
        self.camera_rotation_x = 0.0
        self.camera_rotation_y = 0.0
        self.mouse_dragging = False
        self.last_mouse_pos = (0, 0)
        
        # Animation controls
        self.current_frame = 0
        self.animation_time = 0.0
        self.animation_speed = 1.0
        self.playing = False
        self.show_bones = True
        self.show_bone_names = False
        self.show_hierarchy = True
        
        # Colors
        self.bone_color = (1.0, 1.0, 0.0)  # Yellow
        self.joint_color = (1.0, 0.0, 0.0)  # Red
        self.hierarchy_color = (0.0, 1.0, 0.0)  # Green
        
    def init_pygame(self):
        """Khởi tạo pygame và OpenGL"""
        pygame.init()
        pygame.display.set_mode((self.width, self.height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("ANI Viewer V2 - Real Animation Data")
        
        # Setup OpenGL
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        
        # Lighting setup
        glLightfv(GL_LIGHT0, GL_POSITION, [1.0, 1.0, 1.0, 0.0])
        glLightfv(GL_LIGHT0, GL_DIFFUSE, [1.0, 1.0, 1.0, 1.0])
        glLightfv(GL_LIGHT0, GL_AMBIENT, [0.3, 0.3, 0.3, 1.0])
        
        # Perspective setup
        glMatrixMode(GL_PROJECTION)
        gluPerspective(45, (self.width / self.height), 0.1, 50.0)
        glMatrixMode(GL_MODELVIEW)
        
    def load_ani_file(self, filepath):
        """Load file .ani"""
        try:
            self.parser = ANIParserV3(filepath)
            self.parser.parse_file()
            print(f"Loaded {len(self.parser.bones)} bones with {self.parser.frame_count} animation frames")
            return True
        except Exception as e:
            print(f"Error loading ANI file: {e}")
            return False
            
    def setup_camera(self):
        """Setup camera position và rotation"""
        glLoadIdentity()
        glTranslatef(0.0, 0.0, -self.camera_distance)
        glRotatef(self.camera_rotation_x, 1, 0, 0)
        glRotatef(self.camera_rotation_y, 0, 1, 0)
        
    def get_bone_world_transform(self, bone_name: str, frame: int) -> Transform:
        """Lấy world transform của bone tại frame cụ thể"""
        # Tìm bone
        bone = None
        for b in self.parser.bones:
            if b.name == bone_name:
                bone = b
                break
                
        if not bone:
            return Transform()
            
        # Lấy transform tại frame
        if frame < len(bone.animation_frames):
            anim_transform = bone.animation_frames[frame].transform
            # Combine với bind pose
            pos = anim_transform.position
            if pos == (0.0, 0.0, 0.0):  # Nếu animation không có position data
                pos = bone.bind_pose.position
            return Transform(position=pos, rotation=anim_transform.rotation)
        else:
            return bone.bind_pose
            
    def draw_bone_joint(self, position, size=0.02):
        """Vẽ joint (khớp xương)"""
        glPushMatrix()
        glTranslatef(*position)
        glColor3f(*self.joint_color)
        
        glDisable(GL_LIGHTING)
        quadric = gluNewQuadric()
        gluSphere(quadric, size, 8, 8)
        gluDeleteQuadric(quadric)
        glEnable(GL_LIGHTING)
        
        glPopMatrix()
        
    def draw_bone_connection(self, start_pos, end_pos):
        """Vẽ connection giữa 2 bones"""
        glDisable(GL_LIGHTING)
        glColor3f(*self.hierarchy_color)
        glLineWidth(2.0)
        
        glBegin(GL_LINES)
        glVertex3f(*start_pos)
        glVertex3f(*end_pos)
        glEnd()
        
        glEnable(GL_LIGHTING)
        
    def draw_skeleton_at_frame(self, frame):
        """Vẽ skeleton tại frame cụ thể"""
        if not self.parser or not self.parser.bones:
            return
            
        # Lấy hierarchy
        hierarchy = self.parser.get_bone_hierarchy()
        
        # Lấy world transforms cho tất cả bones
        bone_transforms = {}
        for bone in self.parser.bones:
            transform = self.get_bone_world_transform(bone.name, frame)
            bone_transforms[bone.name] = transform
            
        # Vẽ joints
        if self.show_bones:
            for bone_name, transform in bone_transforms.items():
                # Skip bones với position không hợp lệ
                pos = transform.position
                if (abs(pos[0]) < 100 and abs(pos[1]) < 100 and abs(pos[2]) < 100 and
                    not (math.isnan(pos[0]) or math.isnan(pos[1]) or math.isnan(pos[2]))):
                    self.draw_bone_joint(pos)
                    
        # Vẽ hierarchy connections
        if self.show_hierarchy:
            for child_name, parent_name in hierarchy.items():
                if child_name in bone_transforms and parent_name in bone_transforms:
                    child_pos = bone_transforms[child_name].position
                    parent_pos = bone_transforms[parent_name].position
                    
                    # Kiểm tra positions hợp lệ
                    if (abs(child_pos[0]) < 100 and abs(child_pos[1]) < 100 and abs(child_pos[2]) < 100 and
                        abs(parent_pos[0]) < 100 and abs(parent_pos[1]) < 100 and abs(parent_pos[2]) < 100 and
                        not any(math.isnan(x) for x in child_pos + parent_pos)):
                        self.draw_bone_connection(parent_pos, child_pos)
                        
    def draw_grid(self):
        """Vẽ grid để reference"""
        glDisable(GL_LIGHTING)
        glColor3f(0.3, 0.3, 0.3)
        glLineWidth(1.0)
        
        glBegin(GL_LINES)
        for i in range(-5, 6):
            # Grid lines along X
            glVertex3f(i * 0.2, 0, -1)
            glVertex3f(i * 0.2, 0, 1)
            # Grid lines along Z
            glVertex3f(-1, 0, i * 0.2)
            glVertex3f(1, 0, i * 0.2)
        glEnd()
        
        # Draw axes
        glLineWidth(3.0)
        glBegin(GL_LINES)
        # X axis - Red
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0.5, 0, 0)
        # Y axis - Green
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 0.5, 0)
        # Z axis - Blue
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 0, 0.5)
        glEnd()
        
        glEnable(GL_LIGHTING)
        
    def handle_mouse(self, event):
        """Xử lý mouse input"""
        if event.type == MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.mouse_dragging = True
                self.last_mouse_pos = pygame.mouse.get_pos()
            elif event.button == 4:  # Mouse wheel up
                self.camera_distance = max(0.5, self.camera_distance - 0.2)
            elif event.button == 5:  # Mouse wheel down
                self.camera_distance = min(10.0, self.camera_distance + 0.2)
                
        elif event.type == MOUSEBUTTONUP:
            if event.button == 1:
                self.mouse_dragging = False
                
        elif event.type == MOUSEMOTION and self.mouse_dragging:
            mouse_pos = pygame.mouse.get_pos()
            dx = mouse_pos[0] - self.last_mouse_pos[0]
            dy = mouse_pos[1] - self.last_mouse_pos[1]
            
            self.camera_rotation_y += dx * 0.5
            self.camera_rotation_x += dy * 0.5
            
            # Clamp rotation
            self.camera_rotation_x = max(-90, min(90, self.camera_rotation_x))
            
            self.last_mouse_pos = mouse_pos
            
    def handle_keyboard(self, event):
        """Xử lý keyboard input"""
        if event.type == KEYDOWN:
            if event.key == K_SPACE:
                self.playing = not self.playing
                print(f"Animation {'playing' if self.playing else 'paused'}")
            elif event.key == K_r:
                self.current_frame = 0
                self.animation_time = 0.0
                print("Animation reset")
            elif event.key == K_b:
                self.show_bones = not self.show_bones
                print(f"Bones {'visible' if self.show_bones else 'hidden'}")
            elif event.key == K_h:
                self.show_hierarchy = not self.show_hierarchy
                print(f"Hierarchy {'visible' if self.show_hierarchy else 'hidden'}")
            elif event.key == K_n:
                self.show_bone_names = not self.show_bone_names
                print(f"Bone names {'visible' if self.show_bone_names else 'hidden'}")
            elif event.key == K_PLUS or event.key == K_EQUALS:
                self.animation_speed = min(5.0, self.animation_speed + 0.1)
                print(f"Animation speed: {self.animation_speed:.1f}x")
            elif event.key == K_MINUS:
                self.animation_speed = max(0.1, self.animation_speed - 0.1)
                print(f"Animation speed: {self.animation_speed:.1f}x")
            elif event.key == K_LEFT:
                self.current_frame = max(0, self.current_frame - 1)
                print(f"Frame: {self.current_frame}")
            elif event.key == K_RIGHT:
                if self.parser:
                    self.current_frame = min(self.parser.frame_count - 1, self.current_frame + 1)
                    print(f"Frame: {self.current_frame}")
                    
    def update_animation(self, dt):
        """Update animation"""
        if self.playing and self.parser:
            self.animation_time += dt * self.animation_speed
            
            # Calculate frame từ time
            frame_time = 1.0 / self.parser.fps
            new_frame = int(self.animation_time / frame_time)
            
            if new_frame != self.current_frame:
                self.current_frame = new_frame
                
                # Loop animation
                if self.current_frame >= self.parser.frame_count:
                    self.current_frame = 0
                    self.animation_time = 0.0
                    
    def render(self):
        """Render frame"""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        
        self.setup_camera()
        
        # Draw grid
        self.draw_grid()
        
        # Draw skeleton at current frame
        self.draw_skeleton_at_frame(self.current_frame)
        
        pygame.display.flip()
        
    def print_controls(self):
        """In ra controls"""
        print("\n=== CONTROLS ===")
        print("Mouse: Drag to rotate camera")
        print("Mouse wheel: Zoom in/out")
        print("SPACE: Play/Pause animation")
        print("R: Reset animation")
        print("B: Toggle bones visibility")
        print("H: Toggle hierarchy visibility")
        print("N: Toggle bone names")
        print("LEFT/RIGHT: Step through frames")
        print("+/-: Increase/Decrease animation speed")
        print("ESC: Exit")
        print("================\n")
        
    def run(self, ani_filepath):
        """Main loop"""
        self.init_pygame()
        
        if not self.load_ani_file(ani_filepath):
            print("Failed to load ANI file!")
            return
            
        self.print_controls()
        
        print(f"Animation info:")
        print(f"  Bones: {len(self.parser.bones)}")
        print(f"  Frames: {self.parser.frame_count}")
        print(f"  FPS: {self.parser.fps}")
        
        clock = pygame.time.Clock()
        running = True
        
        while running:
            dt = clock.tick(60) / 1000.0  # Delta time in seconds
            
            for event in pygame.event.get():
                if event.type == QUIT or (event.type == KEYDOWN and event.key == K_ESCAPE):
                    running = False
                else:
                    self.handle_mouse(event)
                    self.handle_keyboard(event)
                    
            self.update_animation(dt)
            self.render()
            
        pygame.quit()

def main():
    if len(sys.argv) != 2:
        print("Usage: python ani_viewer_v2.py <ani_file>")
        print("Example: python ani_viewer_v2.py AMCA0150.ani")
        return
        
    ani_file = sys.argv[1]
    viewer = SkeletonViewerV2()
    viewer.run(ani_file)

if __name__ == "__main__":
    main()
