#!/usr/bin/env python3
"""
MCK Damage Analyzer - Phân tích damage triggers trong file MCK
Với thông tin timing cụ thể từ user
"""

import struct
import os
from typing import List, Tu<PERSON>, Dict

class MCKDamageAnalyzer:
    def __init__(self):
        # Thông tin timing từ user
        self.file_info = {
            'MCK0201.ani': {
                'damage_count': 1, 
                'timing': [0.5],  # 1 damage trước 0.5s
                'description': '1 damage trigger before 0.5s'
            },
            'MCK0202.ani': {
                'damage_count': 2, 
                'timing': [0.5, 1.5],  # 2 damages: trước 0.5s và sau 1.5s
                'description': '2 damage triggers: before 0.5s and after 1.5s'
            }
        }
        
    def analyze_file(self, filename: str):
        """Phân tích một file MCK"""
        if not os.path.exists(filename):
            print(f"File {filename} not found!")
            return
            
        if filename not in self.file_info:
            print(f"No timing info for {filename}")
            return
            
        info = self.file_info[filename]
        
        print(f"\n=== ANALYZING {filename} ===")
        print(f"Expected: {info['damage_count']} damage triggers")
        print(f"Description: {info['description']}")
        print(f"Target timings: {info['timing']}")
        
        with open(filename, 'rb') as f:
            data = f.read()
            
        print(f"File size: {len(data)} bytes")
        
        all_triggers = []
        
        # Tìm damage triggers cho mỗi timing
        for i, target_time in enumerate(info['timing']):
            print(f"\n--- Looking for damage trigger {i+1} around {target_time}s ---")
            
            triggers = self.find_damage_trigger_at_time(data, target_time)
            all_triggers.extend(triggers)
            
            if triggers:
                print(f"✅ Found {len(triggers)} potential triggers:")
                for j, trigger in enumerate(triggers[:3]):  # Show top 3
                    offset, val1, val2, pattern, confidence = trigger
                    if pattern == 'time_first':
                        print(f"  {j+1}. 0x{offset:08x}: Time {val1:.3f}s, Event {val2} (confidence: {confidence:.1f})")
                    else:
                        print(f"  {j+1}. 0x{offset:08x}: Event {val1}, Time {val2:.3f}s (confidence: {confidence:.1f})")
                    
                    # Show hex context
                    start = max(0, offset - 8)
                    end = min(len(data), offset + 16)
                    hex_data = ' '.join(f'{b:02x}' for b in data[start:end])
                    print(f"     Hex: {hex_data}")
            else:
                print(f"❌ No triggers found around {target_time}s")
                
        return all_triggers
        
    def find_damage_trigger_at_time(self, data: bytes, target_time: float) -> List[Tuple]:
        """Tìm damage trigger tại thời điểm cụ thể"""
        matches = []
        
        # Expand search range
        time_tolerance = 0.2  # ±0.2s
        
        for offset in range(0x200, len(data) - 8):
            try:
                # Pattern 1: time (float) + event_type (int)
                time_val = struct.unpack('<f', data[offset:offset+4])[0]
                if abs(time_val - target_time) <= time_tolerance:
                    if offset + 8 <= len(data):
                        next_val = struct.unpack('<I', data[offset+4:offset+8])[0]
                        if 1 <= next_val <= 50:  # Reasonable event type
                            confidence = self.calculate_confidence(time_val, target_time, next_val, 'time_first')
                            matches.append((offset, time_val, next_val, 'time_first', confidence))
                
                # Pattern 2: event_type (int) + time (float)
                if offset + 8 <= len(data):
                    event_type = struct.unpack('<I', data[offset:offset+4])[0]
                    time_val = struct.unpack('<f', data[offset+4:offset+8])[0]
                    
                    if 1 <= event_type <= 50 and abs(time_val - target_time) <= time_tolerance:
                        confidence = self.calculate_confidence(time_val, target_time, event_type, 'event_first')
                        matches.append((offset, event_type, time_val, 'event_first', confidence))
                
                # Pattern 3: byte + time (float)
                if offset + 5 <= len(data):
                    event_byte = data[offset]
                    time_val = struct.unpack('<f', data[offset+1:offset+5])[0]
                    
                    if 1 <= event_byte <= 50 and abs(time_val - target_time) <= time_tolerance:
                        confidence = self.calculate_confidence(time_val, target_time, event_byte, 'byte_first')
                        matches.append((offset, event_byte, time_val, 'byte_first', confidence))
                
                # Pattern 4: short + time (float)
                if offset + 6 <= len(data):
                    event_short = struct.unpack('<H', data[offset:offset+2])[0]
                    time_val = struct.unpack('<f', data[offset+2:offset+6])[0]
                    
                    if 1 <= event_short <= 100 and abs(time_val - target_time) <= time_tolerance:
                        confidence = self.calculate_confidence(time_val, target_time, event_short, 'short_first')
                        matches.append((offset, event_short, time_val, 'short_first', confidence))
                        
            except:
                continue
                
        # Remove duplicates và sort by confidence
        unique_matches = []
        seen_offsets = set()
        for match in matches:
            if match[0] not in seen_offsets:
                unique_matches.append(match)
                seen_offsets.add(match[0])
                
        # Sort by confidence (highest first)
        unique_matches.sort(key=lambda x: x[4], reverse=True)
        
        return unique_matches
        
    def calculate_confidence(self, time_val: float, target_time: float, event_val: int, pattern: str) -> float:
        """Tính confidence score cho damage trigger"""
        confidence = 0.0
        
        # Time accuracy (closer = higher confidence)
        time_diff = abs(time_val - target_time)
        if time_diff <= 0.05:
            confidence += 0.5
        elif time_diff <= 0.1:
            confidence += 0.3
        elif time_diff <= 0.2:
            confidence += 0.1
            
        # Event type reasonableness
        if 1 <= event_val <= 10:
            confidence += 0.3  # Very reasonable
        elif 11 <= event_val <= 20:
            confidence += 0.2  # Reasonable
        elif 21 <= event_val <= 50:
            confidence += 0.1  # Possible
            
        # Pattern type bonus
        if pattern in ['event_first', 'byte_first']:
            confidence += 0.2  # Common patterns
        elif pattern == 'time_first':
            confidence += 0.1
            
        return min(confidence, 1.0)
        
    def compare_files(self):
        """So sánh 2 file MCK để tìm patterns"""
        print("\n=== COMPARING MCK FILES ===")
        
        results = {}
        for filename in self.file_info.keys():
            if os.path.exists(filename):
                results[filename] = self.analyze_file(filename)
                
        # So sánh kết quả
        if len(results) >= 2:
            print(f"\n=== COMPARISON SUMMARY ===")
            
            for filename, triggers in results.items():
                info = self.file_info[filename]
                print(f"\n{filename}:")
                print(f"  Expected: {info['damage_count']} triggers")
                print(f"  Found: {len(triggers)} potential triggers")
                
                if triggers:
                    print(f"  Best matches:")
                    for i, trigger in enumerate(triggers[:info['damage_count']]):
                        offset, val1, val2, pattern, confidence = trigger
                        if pattern == 'time_first':
                            print(f"    {i+1}. Time {val1:.3f}s, Event {val2} (confidence: {confidence:.1f})")
                        else:
                            print(f"    {i+1}. Event {val1}, Time {val2:.3f}s (confidence: {confidence:.1f})")
                            
        return results
        
    def find_common_patterns(self, results: Dict):
        """Tìm patterns chung giữa các file"""
        print(f"\n=== FINDING COMMON PATTERNS ===")
        
        # Collect all high-confidence triggers
        all_triggers = []
        for filename, triggers in results.items():
            for trigger in triggers:
                if trigger[4] >= 0.5:  # High confidence only
                    all_triggers.append((filename, trigger))
                    
        print(f"High-confidence triggers across all files: {len(all_triggers)}")
        
        # Analyze patterns
        pattern_counts = {}
        event_type_counts = {}
        
        for filename, (offset, val1, val2, pattern, confidence) in all_triggers:
            # Count pattern types
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
            
            # Count event types
            if pattern == 'time_first':
                event_type = val2
            else:
                event_type = val1
                
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
            
        print(f"\nPattern distribution:")
        for pattern, count in sorted(pattern_counts.items()):
            print(f"  {pattern}: {count}")
            
        print(f"\nEvent type distribution:")
        for event_type, count in sorted(event_type_counts.items()):
            print(f"  Type {event_type}: {count}")
            
        # Find most likely damage trigger format
        most_common_pattern = max(pattern_counts.items(), key=lambda x: x[1])[0]
        most_common_event = max(event_type_counts.items(), key=lambda x: x[1])[0]
        
        print(f"\n🎯 DAMAGE TRIGGER FORMAT DETECTED:")
        print(f"  Most common pattern: {most_common_pattern}")
        print(f"  Most common event type: {most_common_event}")
        
        return most_common_pattern, most_common_event

def main():
    analyzer = MCKDamageAnalyzer()
    
    print("MCK Damage Trigger Analyzer")
    print("Analyzing MCK files with known damage timing...")
    
    # Analyze both files
    results = analyzer.compare_files()
    
    # Find common patterns
    if results:
        analyzer.find_common_patterns(results)

if __name__ == "__main__":
    main()
