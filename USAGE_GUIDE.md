# Hướng dẫn sử dụng ANI Toolkit

## Khởi động nhanh

### 1. <PERSON><PERSON><PERSON> dễ nhất - Sử dụng Toolkit
```bash
python ani_toolkit.py
```
Toolkit sẽ hiển thị menu với tất cả các tính năng có sẵn.

### 2. Cài đặt dependencies (nếu chưa có)
```bash
pip install --user -r requirements.txt
```

## C<PERSON>c công cụ riêng lẻ

### Phân tích file .ani
```bash
# Phân tích một file cụ thể
python ani_analyzer.py

# Phân tích tất cả file trong thư mục
python batch_analyzer.py
```

### Xem 3D Viewer
```bash
python ani_viewer.py AMCA0150.ani
```

**Controls trong viewer:**
- **Mouse drag**: Xoay camera
- **Mouse wheel**: Zoom in/out
- **SPACE**: Play/Pause animation
- **R**: Reset animation
- **B**: Toggle hiển thị bones
- **N**: Toggle hiển thị tên bones
- **+/-**: Tăng/giảm tốc độ animation
- **ESC**: Thoát

### Export dữ liệu
```bash
python ani_exporter.py AMCA0150.ani
```

Sẽ tạo ra:
- `AMCA0150.json` - Dữ liệu đầy đủ
- `AMCA0150_skeleton.obj` - Cấu trúc skeleton
- `AMCA0150_animation.csv` - Dữ liệu animation
- `AMCA0150_hierarchy.txt` - Cây phân cấp bones

## Kết quả đã đạt được

### Phân tích thành công
✅ **11/11 file .ani** được parse thành công (100% success rate)

✅ **60-62 bones** được detect trong mỗi file

✅ **Bone hierarchy** được xây dựng đúng:
```
Bip01 (root)
├── Bip01 Pelvis
│   ├── Bip01 Spine → Bip01 Spine1 → Bip01 Neck → Bip01 Head
│   ├── Arms: L/R Clavicle → UpperArm → Forearm → Hand → Fingers
│   └── Legs: L/R Thigh → Calf → Foot → Toe0
└── Custom bones: Bone01-12, Bone_a1-a4, Bone_b1-b4, etc.
```

### Cấu trúc file .ani được hiểu
✅ **Header**: Thông tin file

✅ **Bone data**: 
- Tên bone (null-terminated string)
- Transform matrix (position + rotation)
- Animation keyframes

✅ **Animation data**:
- Frame count
- Keyframe data (time + transform)

### Thống kê từ batch analysis
- **File size**: 23,333 - 44,605 bytes
- **Bone count**: 60-62 bones/file
- **Common bones**: 60 bones có trong tất cả file
- **Unique bones**: 63 bones tổng cộng

## Các format được hỗ trợ

### Input
- ✅ `.ani` files (DirectX9 animation format)

### Output
- ✅ **JSON**: Dữ liệu đầy đủ (bones + animation)
- ✅ **OBJ**: Cấu trúc skeleton (vertices + lines)
- ✅ **CSV**: Animation data (time + transforms)
- ✅ **TXT**: Bone hierarchy tree

## Troubleshooting

### Lỗi thường gặp

**1. Import Error - PyOpenGL**
```bash
pip install --user PyOpenGL PyOpenGL_accelerate
```

**2. Permission Error khi cài đặt**
```bash
pip install --user -r requirements.txt
```

**3. Viewer không mở được**
- Kiểm tra driver graphics card
- Thử chạy với software rendering

**4. File .ani không load được**
- Kiểm tra file có tồn tại không
- Đảm bảo file không bị corrupt
- Thử với file khác

### Performance Tips

**Viewer chạy chậm:**
- Giảm số bones hiển thị (nhấn B)
- Tắt bone names (nhấn N)
- Giảm tốc độ animation (nhấn -)

## Ví dụ sử dụng

### 1. Phân tích nhanh một file
```bash
python ani_toolkit.py
# Chọn 1 → Chọn file → Xem kết quả
```

### 2. Xem animation 3D
```bash
python ani_toolkit.py
# Chọn 2 → Chọn file → Điều khiển bằng mouse/keyboard
```

### 3. Export để sử dụng trong tool khác
```bash
python ani_toolkit.py
# Chọn 3 → Chọn file → Nhận các file output
```

### 4. So sánh nhiều file
```bash
python ani_toolkit.py
# Chọn 4 → Xem analysis_report.json
```

## Mở rộng

### Thêm tính năng mới
- Sửa đổi `ani_analyzer.py` để parse thêm data
- Thêm export format mới trong `ani_exporter.py`
- Cải thiện rendering trong `ani_viewer.py`

### Hỗ trợ format khác
- Tạo parser mới theo pattern của `ANIFile`
- Implement interface tương tự cho format mới
- Tích hợp vào toolkit

## Kết luận

ANI Toolkit đã thành công trong việc:

1. **Reverse engineer** format file .ani
2. **Parse** được 100% file test
3. **Hiển thị** skeleton 3D với animation
4. **Export** sang nhiều format phổ biến
5. **Tạo tools** dễ sử dụng cho end-user

Project này có thể được sử dụng để:
- Nghiên cứu game assets
- Convert animation sang format khác
- Tạo tools modding cho game
- Học tập về 3D graphics programming
