# ANI File Viewer - 3D Skeleton Animation Viewer

Một tool để phân tích và hiển thị file animation (.ani) từ game 3D sử dụng DirectX9.

## Tính năng

- **Phân tích file .ani**: Parse cấu trúc file và extract thông tin skeleton
- **Hiển thị 3D**: Viewer 3D với OpenGL để hiển thị skeleton
- **Animation playback**: Play/pause animation với điều khiển tốc độ
- **Camera controls**: Xoay, zoom camera để xem từ nhiều góc độ
- **Bone hierarchy**: Hiển thị đúng mối quan hệ parent-child của bones

## Cài đặt

### Yêu cầu hệ thống
- Python 3.7+
- Windows (đã test trên Windows)

### Cài đặt dependencies

```bash
pip install -r requirements.txt
```

Hoặc cài đặt thủ công:
```bash
pip install pygame PyOpenGL PyOpenGL_accelerate numpy
```

## Sử dụng

### Chạy viewer cho một file cụ thể:
```bash
python ani_viewer.py AMCA0150.ani
```

### Chạy setup script (tự động chọn file):
```bash
python setup_and_run.py
```

### Phân tích file mà không hiển thị:
```bash
python ani_analyzer.py
```

## Controls

- **Mouse drag**: Xoay camera
- **Mouse wheel**: Zoom in/out
- **SPACE**: Play/Pause animation
- **R**: Reset animation về frame đầu
- **B**: Toggle hiển thị bones
- **N**: Toggle hiển thị tên bones
- **+/-**: Tăng/giảm tốc độ animation
- **ESC**: Thoát

## Cấu trúc file .ani

Dựa trên phân tích, file .ani có cấu trúc:

1. **Header**: Thông tin chung về file
2. **Bone data**: 
   - Tên bone (null-terminated string)
   - Transform matrix (position + rotation)
   - Animation keyframes
3. **Animation data**:
   - Frame count
   - Keyframe data (time + transform)

### Bones được hỗ trợ

- **Bip01 hierarchy**: Skeleton chính (Pelvis, Spine, Head, Arms, Legs)
- **Custom bones**: Bone01-12, Bone_a1-a4, Bone_b1-b4, etc.
- **Accessories**: Ponytail, weapons, etc.

## Kết quả phân tích

Từ file AMCA0150.ani đã parse được:
- **61 bones** với hierarchy đầy đủ
- **Transform data** cho mỗi bone
- **Animation tracks** với keyframes

### Bone hierarchy được detect:
```
Bip01 (root)
├── Bip01 Pelvis
│   ├── Bip01 Spine
│   │   ├── Bip01 Spine1
│   │   │   ├── Bip01 Neck
│   │   │   │   └── Bip01 Head
│   │   │   ├── Bip01 L Clavicle
│   │   │   │   └── Bip01 L UpperArm
│   │   │   │       └── Bip01 L Forearm
│   │   │   │           └── Bip01 L Hand
│   │   │   └── Bip01 R Clavicle (similar structure)
│   ├── Bip01 L Thigh
│   │   └── Bip01 L Calf
│   │       └── Bip01 L Foot
│   │           └── Bip01 L Toe0
│   └── Bip01 R Thigh (similar structure)
└── Custom bones (Bone01-12, Bone_a1-a4, etc.)
```

## Troubleshooting

### Lỗi cài đặt dependencies
```bash
# Nếu gặp lỗi permission:
pip install --user -r requirements.txt

# Hoặc chạy với admin privileges
```

### Lỗi OpenGL
- Đảm bảo driver graphics card được update
- Thử chạy với software rendering nếu cần

### File không load được
- Kiểm tra file .ani có tồn tại không
- Đảm bảo file không bị corrupt
- Thử với file .ani khác

## Phát triển thêm

### Tính năng có thể thêm:
- [ ] Export skeleton sang format khác (FBX, OBJ)
- [ ] Import/export animation data
- [ ] Texture mapping cho bones
- [ ] Advanced animation blending
- [ ] Bone weight visualization
- [ ] Animation timeline scrubbing

### Cải thiện parser:
- [ ] Hiểu rõ hơn format animation data
- [ ] Parse rotation data chính xác hơn
- [ ] Support thêm loại file animation khác

## License

MIT License - Tự do sử dụng và modify.

## Credits

Phát triển bởi AI Assistant để hỗ trợ reverse engineering file animation game 3D.
