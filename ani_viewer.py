#!/usr/bin/env python3
"""
3D Viewer cho file .ani - Animation files từ game 3D DirectX9
Sử dụng pygame và OpenGL để hiển thị skeleton và animation
"""

import pygame
import sys
import math
import numpy as np
from pygame.locals import *
from ani_analyzer import ANIFile, Bone, KeyFrame

try:
    from OpenGL.GL import *
    from OpenGL.GLU import *
    from OpenGL.GLUT import *
except ImportError:
    print("Cần cài đặt PyOpenGL: pip install PyOpenGL PyOpenGL_accelerate")
    sys.exit(1)

class SkeletonViewer:
    def __init__(self, width=1024, height=768):
        self.width = width
        self.height = height
        self.ani_file = None
        
        # Camera controls
        self.camera_distance = 5.0
        self.camera_rotation_x = 0.0
        self.camera_rotation_y = 0.0
        self.mouse_dragging = False
        self.last_mouse_pos = (0, 0)
        
        # Animation controls
        self.animation_time = 0.0
        self.animation_speed = 1.0
        self.playing = False
        self.show_bones = True
        self.show_bone_names = True
        
        # Colors
        self.bone_color = (1.0, 1.0, 0.0)  # Yellow
        self.joint_color = (1.0, 0.0, 0.0)  # Red
        self.text_color = (1.0, 1.0, 1.0)  # White
        
    def init_pygame(self):
        """Khởi tạo pygame và OpenGL"""
        pygame.init()
        pygame.display.set_mode((self.width, self.height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("ANI File Viewer - 3D Skeleton Animation")
        
        # Setup OpenGL
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        
        # Lighting setup
        glLightfv(GL_LIGHT0, GL_POSITION, [1.0, 1.0, 1.0, 0.0])
        glLightfv(GL_LIGHT0, GL_DIFFUSE, [1.0, 1.0, 1.0, 1.0])
        glLightfv(GL_LIGHT0, GL_AMBIENT, [0.3, 0.3, 0.3, 1.0])
        
        # Material setup
        glMaterialfv(GL_FRONT, GL_DIFFUSE, [0.8, 0.8, 0.8, 1.0])
        glMaterialfv(GL_FRONT, GL_SPECULAR, [1.0, 1.0, 1.0, 1.0])
        glMaterialf(GL_FRONT, GL_SHININESS, 50.0)
        
        # Perspective setup
        glMatrixMode(GL_PROJECTION)
        gluPerspective(45, (self.width / self.height), 0.1, 50.0)
        glMatrixMode(GL_MODELVIEW)
        
    def load_ani_file(self, filepath):
        """Load file .ani"""
        try:
            self.ani_file = ANIFile(filepath)
            self.ani_file.analyze_structure()
            print(f"Loaded {len(self.ani_file.bones)} bones")
            return True
        except Exception as e:
            print(f"Error loading ANI file: {e}")
            return False
            
    def setup_camera(self):
        """Setup camera position và rotation"""
        glLoadIdentity()
        glTranslatef(0.0, 0.0, -self.camera_distance)
        glRotatef(self.camera_rotation_x, 1, 0, 0)
        glRotatef(self.camera_rotation_y, 0, 1, 0)
        
    def draw_bone(self, bone: Bone, parent_pos=None):
        """Vẽ một bone"""
        if not bone:
            return
            
        bone_pos = bone.position
        
        # Vẽ joint (khớp xương)
        glPushMatrix()
        glTranslatef(*bone_pos)
        glColor3f(*self.joint_color)
        
        # Vẽ sphere cho joint
        glDisable(GL_LIGHTING)
        quadric = gluNewQuadric()
        gluSphere(quadric, 0.02, 8, 8)
        gluDeleteQuadric(quadric)
        glEnable(GL_LIGHTING)
        
        glPopMatrix()
        
        # Vẽ bone connection đến parent
        if parent_pos:
            glDisable(GL_LIGHTING)
            glColor3f(*self.bone_color)
            glLineWidth(3.0)
            
            glBegin(GL_LINES)
            glVertex3f(*parent_pos)
            glVertex3f(*bone_pos)
            glEnd()
            
            glEnable(GL_LIGHTING)
            
    def build_bone_hierarchy(self):
        """Xây dựng hierarchy của bones dựa trên tên"""
        if not self.ani_file or not self.ani_file.bones:
            return {}

        bone_map = {bone.name: bone for bone in self.ani_file.bones}
        hierarchy = {}

        # Định nghĩa parent-child relationships dựa trên naming convention
        parent_rules = [
            # Spine hierarchy
            ("Bip01 Spine", "Bip01 Pelvis"),
            ("Bip01 Spine1", "Bip01 Spine"),
            ("Bip01 Neck", "Bip01 Spine1"),
            ("Bip01 Head", "Bip01 Neck"),

            # Left arm
            ("Bip01 L Clavicle", "Bip01 Spine1"),
            ("Bip01 L UpperArm", "Bip01 L Clavicle"),
            ("Bip01 L Forearm", "Bip01 L UpperArm"),
            ("Bip01 L Hand", "Bip01 L Forearm"),
            ("Bip01 L Finger0", "Bip01 L Hand"),
            ("Bip01 L Finger01", "Bip01 L Finger0"),

            # Right arm
            ("Bip01 R Clavicle", "Bip01 Spine1"),
            ("Bip01 R UpperArm", "Bip01 R Clavicle"),
            ("Bip01 R Forearm", "Bip01 R UpperArm"),
            ("Bip01 R Hand", "Bip01 R Forearm"),
            ("Bip01 R Finger0", "Bip01 R Hand"),
            ("Bip01 R Finger01", "Bip01 R Finger0"),

            # Left leg
            ("Bip01 L Thigh", "Bip01 Pelvis"),
            ("Bip01 L Calf", "Bip01 L Thigh"),
            ("Bip01 L Foot", "Bip01 L Calf"),
            ("Bip01 L Toe0", "Bip01 L Foot"),

            # Right leg
            ("Bip01 R Thigh", "Bip01 Pelvis"),
            ("Bip01 R Calf", "Bip01 R Thigh"),
            ("Bip01 R Foot", "Bip01 R Calf"),
            ("Bip01 R Toe0", "Bip01 R Foot"),
        ]

        # Áp dụng rules
        for child_name, parent_name in parent_rules:
            if child_name in bone_map and parent_name in bone_map:
                hierarchy[child_name] = parent_name

        # Thêm các bones khác (Bone01, Bone02, etc.) với parent là root
        for bone in self.ani_file.bones:
            if bone.name not in hierarchy and bone.name != "Bip01":
                if bone.name.startswith("Bone") or bone.name.startswith("Bip01 Ponytail"):
                    hierarchy[bone.name] = "Bip01"

        return hierarchy

    def draw_skeleton(self):
        """Vẽ toàn bộ skeleton với hierarchy"""
        if not self.ani_file or not self.ani_file.bones:
            return

        bone_map = {bone.name: bone for bone in self.ani_file.bones}
        hierarchy = self.build_bone_hierarchy()

        # Vẽ tất cả bones với connections
        for bone in self.ani_file.bones:
            parent_pos = None

            # Tìm parent position từ hierarchy
            if bone.name in hierarchy:
                parent_name = hierarchy[bone.name]
                if parent_name in bone_map:
                    parent_pos = bone_map[parent_name].position

            self.draw_bone(bone, parent_pos)

            # Vẽ bone name nếu enabled
            if self.show_bone_names:
                self.draw_bone_name(bone)

    def draw_bone_name(self, bone):
        """Vẽ tên bone tại vị trí bone"""
        # Simplified text rendering - trong thực tế cần implement proper text rendering
        # Có thể sử dụng pygame font và texture mapping
        pass
            
    def draw_grid(self):
        """Vẽ grid để reference"""
        glDisable(GL_LIGHTING)
        glColor3f(0.3, 0.3, 0.3)
        glLineWidth(1.0)
        
        glBegin(GL_LINES)
        for i in range(-10, 11):
            # Grid lines along X
            glVertex3f(i * 0.5, 0, -5)
            glVertex3f(i * 0.5, 0, 5)
            # Grid lines along Z
            glVertex3f(-5, 0, i * 0.5)
            glVertex3f(5, 0, i * 0.5)
        glEnd()
        
        # Draw axes
        glLineWidth(3.0)
        glBegin(GL_LINES)
        # X axis - Red
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(1, 0, 0)
        # Y axis - Green
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 1, 0)
        # Z axis - Blue
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0, 0, 0)
        glVertex3f(0, 0, 1)
        glEnd()
        
        glEnable(GL_LIGHTING)
        
    def handle_mouse(self, event):
        """Xử lý mouse input cho camera control"""
        if event.type == MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.mouse_dragging = True
                self.last_mouse_pos = pygame.mouse.get_pos()
            elif event.button == 4:  # Mouse wheel up
                self.camera_distance = max(1.0, self.camera_distance - 0.5)
            elif event.button == 5:  # Mouse wheel down
                self.camera_distance = min(20.0, self.camera_distance + 0.5)
                
        elif event.type == MOUSEBUTTONUP:
            if event.button == 1:
                self.mouse_dragging = False
                
        elif event.type == MOUSEMOTION and self.mouse_dragging:
            mouse_pos = pygame.mouse.get_pos()
            dx = mouse_pos[0] - self.last_mouse_pos[0]
            dy = mouse_pos[1] - self.last_mouse_pos[1]
            
            self.camera_rotation_y += dx * 0.5
            self.camera_rotation_x += dy * 0.5
            
            # Clamp rotation
            self.camera_rotation_x = max(-90, min(90, self.camera_rotation_x))
            
            self.last_mouse_pos = mouse_pos
            
    def handle_keyboard(self, event):
        """Xử lý keyboard input"""
        if event.type == KEYDOWN:
            if event.key == K_SPACE:
                self.playing = not self.playing
                print(f"Animation {'playing' if self.playing else 'paused'}")
            elif event.key == K_r:
                self.animation_time = 0.0
                print("Animation reset")
            elif event.key == K_b:
                self.show_bones = not self.show_bones
                print(f"Bones {'visible' if self.show_bones else 'hidden'}")
            elif event.key == K_n:
                self.show_bone_names = not self.show_bone_names
                print(f"Bone names {'visible' if self.show_bone_names else 'hidden'}")
            elif event.key == K_PLUS or event.key == K_EQUALS:
                self.animation_speed = min(5.0, self.animation_speed + 0.1)
                print(f"Animation speed: {self.animation_speed:.1f}x")
            elif event.key == K_MINUS:
                self.animation_speed = max(0.1, self.animation_speed - 0.1)
                print(f"Animation speed: {self.animation_speed:.1f}x")
                
    def update_animation(self, dt):
        """Update animation time"""
        if self.playing:
            self.animation_time += dt * self.animation_speed
            # Loop animation (giả sử 3 giây)
            if self.animation_time > 3.0:
                self.animation_time = 0.0
                
    def render(self):
        """Render frame"""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        
        self.setup_camera()
        
        # Draw grid
        self.draw_grid()
        
        # Draw skeleton
        if self.show_bones:
            self.draw_skeleton()
            
        pygame.display.flip()
        
    def print_controls(self):
        """In ra controls"""
        print("\n=== CONTROLS ===")
        print("Mouse: Drag to rotate camera")
        print("Mouse wheel: Zoom in/out")
        print("SPACE: Play/Pause animation")
        print("R: Reset animation")
        print("B: Toggle bones visibility")
        print("N: Toggle bone names")
        print("+/-: Increase/Decrease animation speed")
        print("ESC: Exit")
        print("================\n")
        
    def run(self, ani_filepath):
        """Main loop"""
        self.init_pygame()
        
        if not self.load_ani_file(ani_filepath):
            print("Failed to load ANI file!")
            return
            
        self.print_controls()
        
        clock = pygame.time.Clock()
        running = True
        
        while running:
            dt = clock.tick(60) / 1000.0  # Delta time in seconds
            
            for event in pygame.event.get():
                if event.type == QUIT or (event.type == KEYDOWN and event.key == K_ESCAPE):
                    running = False
                else:
                    self.handle_mouse(event)
                    self.handle_keyboard(event)
                    
            self.update_animation(dt)
            self.render()
            
        pygame.quit()

def main():
    if len(sys.argv) != 2:
        print("Usage: python ani_viewer.py <ani_file>")
        print("Example: python ani_viewer.py AMCA0150.ani")
        return
        
    ani_file = sys.argv[1]
    viewer = SkeletonViewer()
    viewer.run(ani_file)

if __name__ == "__main__":
    main()
