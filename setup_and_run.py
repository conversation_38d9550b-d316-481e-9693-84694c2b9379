#!/usr/bin/env python3
"""
Script để cài đặt dependencies và chạy ANI viewer
"""

import subprocess
import sys
import os

def install_dependencies():
    """Cài đặt các dependencies cần thiết"""
    print("Installing dependencies...")
    
    try:
        # Cài đặt từ requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing dependencies: {e}")
        return False

def check_ani_files():
    """Kiểm tra xem có file .ani nào không"""
    ani_files = [f for f in os.listdir('.') if f.endswith('.ani')]
    
    if not ani_files:
        print("✗ No .ani files found in current directory!")
        return None
    
    print(f"✓ Found {len(ani_files)} .ani files:")
    for i, f in enumerate(ani_files):
        print(f"  {i+1}. {f}")
    
    return ani_files

def main():
    print("=== ANI File Viewer Setup ===")
    
    # Cài đặt dependencies
    if not install_dependencies():
        return
    
    # Kiểm tra file .ani
    ani_files = check_ani_files()
    if not ani_files:
        return
    
    # Chọn file để view
    if len(ani_files) == 1:
        selected_file = ani_files[0]
        print(f"Using file: {selected_file}")
    else:
        try:
            choice = input(f"\nSelect file to view (1-{len(ani_files)}): ")
            idx = int(choice) - 1
            if 0 <= idx < len(ani_files):
                selected_file = ani_files[idx]
            else:
                print("Invalid choice!")
                return
        except ValueError:
            print("Invalid input!")
            return
    
    # Chạy viewer
    print(f"\nLaunching viewer for {selected_file}...")
    print("Controls:")
    print("- Mouse: Drag to rotate camera")
    print("- Mouse wheel: Zoom in/out")
    print("- SPACE: Play/Pause animation")
    print("- R: Reset animation")
    print("- B: Toggle bones visibility")
    print("- ESC: Exit")
    
    try:
        subprocess.run([sys.executable, "ani_viewer.py", selected_file])
    except KeyboardInterrupt:
        print("\nViewer closed.")
    except Exception as e:
        print(f"Error running viewer: {e}")

if __name__ == "__main__":
    main()
