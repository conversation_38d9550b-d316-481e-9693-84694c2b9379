#!/usr/bin/env python3
"""
Final Damage Analyzer - Analyzer ch<PERSON>h xác cho damage triggers
Dựa trên pattern đã tìm thấy: byte + float time
"""

import struct
import os
from typing import List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class DamageTrigger:
    offset: int
    event_type: int
    time: float
    confidence: float
    description: str

class FinalDamageAnalyzer:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.data = None
        self.triggers: List[DamageTrigger] = []
        
    def load_file(self):
        """Load file vào memory"""
        with open(self.filepath, 'rb') as f:
            self.data = f.read()
        print(f"Analyzing {os.path.basename(self.filepath)} ({len(self.data)} bytes)")
        
    def find_damage_triggers(self) -> List[DamageTrigger]:
        """Tìm damage triggers sử dụng pattern đã detect"""
        triggers = []
        
        # Pattern: byte (event_type) + float (time)
        for offset in range(0x200, len(self.data) - 5):
            try:
                event_type = self.data[offset]
                time_val = struct.unpack('<f', self.data[offset+1:offset+5])[0]
                
                # Filter criteria dựa trên kết quả MCK analysis
                if (1 <= event_type <= 100 and  # Reasonable event type range
                    0.1 <= time_val <= 10.0):    # Reasonable time range
                    
                    confidence = self.calculate_confidence(event_type, time_val, offset)
                    
                    if confidence >= 0.5:  # Only high confidence triggers
                        description = self.get_trigger_description(event_type, time_val)
                        
                        trigger = DamageTrigger(
                            offset=offset,
                            event_type=event_type,
                            time=time_val,
                            confidence=confidence,
                            description=description
                        )
                        triggers.append(trigger)
                        
            except:
                continue
                
        # Remove duplicates và sort by time
        unique_triggers = self.remove_duplicates(triggers)
        unique_triggers.sort(key=lambda x: x.time)
        
        return unique_triggers
        
    def calculate_confidence(self, event_type: int, time_val: float, offset: int) -> float:
        """Tính confidence dựa trên patterns từ MCK analysis"""
        confidence = 0.0
        
        # Event type confidence (dựa trên MCK results)
        high_confidence_types = [13, 28, 40, 49, 50]  # Từ MCK analysis
        medium_confidence_types = [21, 23, 38, 60, 61, 63]
        
        if event_type in high_confidence_types:
            confidence += 0.4
        elif event_type in medium_confidence_types:
            confidence += 0.3
        elif 1 <= event_type <= 20:
            confidence += 0.2
        else:
            confidence += 0.1
            
        # Time reasonableness
        if 0.2 <= time_val <= 5.0:
            confidence += 0.3  # Reasonable attack timing
        elif 0.1 <= time_val <= 10.0:
            confidence += 0.2
        else:
            confidence += 0.1
            
        # Context analysis
        context_score = self.analyze_context(offset)
        confidence += context_score * 0.3
        
        return min(confidence, 1.0)
        
    def analyze_context(self, offset: int) -> float:
        """Phân tích context xung quanh trigger"""
        score = 0.0
        
        try:
            # Check for animation data patterns around offset
            start = max(0, offset - 16)
            end = min(len(self.data), offset + 16)
            context = self.data[start:end]
            
            # Look for float patterns (animation data)
            float_count = 0
            for i in range(0, len(context) - 4, 4):
                try:
                    val = struct.unpack('<f', context[i:i+4])[0]
                    if abs(val) < 100:  # Reasonable values
                        float_count += 1
                except:
                    pass
                    
            if float_count >= 2:
                score += 0.5
                
            # Check for bone-related data nearby
            for i in range(max(0, offset - 100), min(len(self.data), offset + 100)):
                if self.data[i:i+5] == b'Bip01' or self.data[i:i+4] == b'Bone':
                    score += 0.3
                    break
                    
        except:
            pass
            
        return min(score, 1.0)
        
    def get_trigger_description(self, event_type: int, time_val: float) -> str:
        """Tạo mô tả cho damage trigger"""
        # Dựa trên MCK analysis results
        type_descriptions = {
            13: "Primary damage trigger",
            28: "Secondary damage trigger", 
            40: "Special attack trigger",
            49: "Combo damage trigger",
            50: "Main damage trigger",
            21: "Effect trigger",
            23: "Sound trigger",
            38: "Animation event",
            60: "End attack trigger",
            61: "Critical hit trigger",
            63: "Finisher trigger"
        }
        
        base_desc = type_descriptions.get(event_type, f"Damage event type {event_type}")
        
        # Add timing description
        if time_val < 0.5:
            timing_desc = "Early attack"
        elif time_val < 1.0:
            timing_desc = "Mid attack"
        elif time_val < 2.0:
            timing_desc = "Late attack"
        else:
            timing_desc = "Very late attack"
            
        return f"{base_desc} ({timing_desc})"
        
    def remove_duplicates(self, triggers: List[DamageTrigger]) -> List[DamageTrigger]:
        """Remove duplicate triggers"""
        unique = []
        
        for trigger in triggers:
            is_duplicate = False
            
            for existing in unique:
                # Consider duplicate if time difference < 0.05s and offset difference < 10
                if (abs(trigger.time - existing.time) < 0.05 and 
                    abs(trigger.offset - existing.offset) < 10):
                    # Keep the one with higher confidence
                    if trigger.confidence > existing.confidence:
                        unique.remove(existing)
                        unique.append(trigger)
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                unique.append(trigger)
                
        return unique
        
    def analyze_file(self):
        """Main analysis function"""
        self.load_file()
        self.triggers = self.find_damage_triggers()
        
        print(f"\n=== DAMAGE TRIGGER ANALYSIS ===")
        print(f"File: {os.path.basename(self.filepath)}")
        print(f"Found {len(self.triggers)} damage triggers:")
        
        if not self.triggers:
            print("❌ No damage triggers found!")
            return []
            
        for i, trigger in enumerate(self.triggers):
            print(f"\n🎯 Damage Trigger {i+1}:")
            print(f"   ⏰ Time: {trigger.time:.3f} seconds from animation start")
            print(f"   🔢 Event Type: {trigger.event_type}")
            print(f"   📝 Description: {trigger.description}")
            print(f"   ✅ Confidence: {trigger.confidence:.1%}")
            print(f"   📍 File Offset: 0x{trigger.offset:08x}")
            
            # Show hex context
            start = max(0, trigger.offset - 8)
            end = min(len(self.data), trigger.offset + 12)
            hex_data = ' '.join(f'{b:02x}' for b in self.data[start:end])
            print(f"   🔍 Hex: {hex_data}")
            
        # Summary
        print(f"\n=== SUMMARY ===")
        print(f"🗡️ Attack Animation: {os.path.basename(self.filepath)}")
        print(f"⚔️ Total Damage Triggers: {len(self.triggers)}")
        
        if self.triggers:
            earliest = min(trigger.time for trigger in self.triggers)
            latest = max(trigger.time for trigger in self.triggers)
            print(f"⏱️ Damage Window: {earliest:.3f}s - {latest:.3f}s")
            print(f"🎯 Primary Damage Time: {self.triggers[0].time:.3f}s")
            
            # List all trigger times
            print(f"📋 All Damage Times:")
            for i, trigger in enumerate(self.triggers):
                print(f"   {i+1}. {trigger.time:.3f}s (Type {trigger.event_type})")
                
        return self.triggers
        
    def verify_with_known_data(self):
        """Verify results với known data từ MCK files"""
        filename = os.path.basename(self.filepath)
        
        known_data = {
            'MCK0201.ani': {'expected_count': 1, 'expected_times': [0.5]},
            'MCK0202.ani': {'expected_count': 2, 'expected_times': [0.5, 1.5]},
            'AMCA0150.ani': {'expected_count': 1, 'expected_times': [0.3]}  # Gần cuối animation
        }
        
        if filename in known_data:
            expected = known_data[filename]
            print(f"\n=== VERIFICATION ===")
            print(f"Expected: {expected['expected_count']} triggers around {expected['expected_times']}")
            print(f"Found: {len(self.triggers)} triggers")
            
            # Check if found triggers match expected
            matches = 0
            for expected_time in expected['expected_times']:
                for trigger in self.triggers:
                    if abs(trigger.time - expected_time) <= 0.2:  # ±0.2s tolerance
                        matches += 1
                        break
                        
            print(f"Matches: {matches}/{expected['expected_count']}")
            
            if matches == expected['expected_count']:
                print("✅ VERIFICATION PASSED!")
            else:
                print("⚠️ Partial match - may need refinement")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python final_damage_analyzer.py <ani_file>")
        print("Example: python final_damage_analyzer.py MCK0201.ani")
        return
        
    analyzer = FinalDamageAnalyzer(sys.argv[1])
    triggers = analyzer.analyze_file()
    analyzer.verify_with_known_data()

if __name__ == "__main__":
    main()
